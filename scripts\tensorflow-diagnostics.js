/**
 * FootFit TensorFlow.js Diagnostics Script
 * Analyzes TensorFlow.js setup and identifies integration issues
 */

const fs = require('fs');
const path = require('path');

class TensorFlowDiagnostics {
  constructor() {
    this.issues = [];
    this.recommendations = [];
    this.diagnosticResults = {
      dependencies: false,
      setup: false,
      modelFiles: false,
      compatibility: false,
      overallScore: 0
    };
  }

  async runDiagnostics() {
    console.log('🔍 FootFit TensorFlow.js Diagnostics');
    console.log('====================================\n');

    // Test 1: Check Dependencies
    await this.checkDependencies();
    
    // Test 2: Analyze Setup Configuration
    await this.analyzeSetupConfiguration();
    
    // Test 3: Check Model Files
    await this.checkModelFiles();
    
    // Test 4: Test Compatibility
    await this.testCompatibility();
    
    // Generate report
    this.generateDiagnosticReport();
    
    return this.diagnosticResults;
  }

  async checkDependencies() {
    console.log('🔍 Test 1: Checking TensorFlow.js Dependencies');
    console.log('===============================================');
    
    try {
      // Check package.json
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      const deps = packageJson.dependencies || {};
      
      const requiredTFDeps = [
        '@tensorflow/tfjs',
        '@tensorflow/tfjs-backend-cpu',
        '@tensorflow/tfjs-backend-webgl',
        '@tensorflow/tfjs-core',
        '@tensorflow/tfjs-react-native'
      ];

      let depsOk = 0;
      
      console.log('  📦 TensorFlow.js Dependencies:');
      for (const dep of requiredTFDeps) {
        if (deps[dep]) {
          console.log(`    ✅ ${dep}: ${deps[dep]}`);
          depsOk++;
        } else {
          console.log(`    ❌ ${dep}: Missing`);
          this.issues.push(`Missing dependency: ${dep}`);
        }
      }

      // Check for potential conflicts
      const potentialConflicts = [
        'tensorflow',
        '@tensorflow/tfjs-node',
        '@tensorflow/tfjs-node-gpu'
      ];

      console.log('\n  🔍 Checking for conflicts:');
      for (const conflict of potentialConflicts) {
        if (deps[conflict]) {
          console.log(`    ⚠️  ${conflict}: ${deps[conflict]} (potential conflict)`);
          this.issues.push(`Potential conflict: ${conflict} should not be used in React Native`);
        } else {
          console.log(`    ✅ ${conflict}: Not present (good)`);
        }
      }

      // Check Expo dependencies
      const expoGLDeps = ['expo-gl', 'expo-gl-cpp'];
      console.log('\n  📱 Expo GL Dependencies:');
      for (const dep of expoGLDeps) {
        if (deps[dep]) {
          console.log(`    ✅ ${dep}: ${deps[dep]}`);
        } else {
          console.log(`    ⚠️  ${dep}: Missing (needed for WebGL backend)`);
          this.recommendations.push(`Add ${dep} for WebGL backend support`);
        }
      }

      this.diagnosticResults.dependencies = depsOk === requiredTFDeps.length;
      console.log(`\n📊 Dependencies: ${this.diagnosticResults.dependencies ? 'PASS' : 'FAIL'} (${depsOk}/${requiredTFDeps.length})\n`);

    } catch (error) {
      console.log(`❌ Failed to check dependencies: ${error.message}\n`);
      this.issues.push(`Failed to check dependencies: ${error.message}`);
    }
  }

  async analyzeSetupConfiguration() {
    console.log('🔍 Test 2: Analyzing Setup Configuration');
    console.log('========================================');
    
    try {
      // Check tfjs-setup.ts file
      const setupPath = 'utils/tfjs-setup.ts';
      if (fs.existsSync(setupPath)) {
        console.log('  ✅ TensorFlow.js setup file exists');
        
        const setupContent = fs.readFileSync(setupPath, 'utf8');
        
        // Check import order
        const importOrder = [
          '@tensorflow/tfjs-react-native',
          '@tensorflow/tfjs',
          '@tensorflow/tfjs-backend-cpu',
          '@tensorflow/tfjs-backend-webgl'
        ];

        console.log('  📋 Import order analysis:');
        let importOrderCorrect = true;
        let lastIndex = -1;
        
        for (const importName of importOrder) {
          const index = setupContent.indexOf(importName);
          if (index === -1) {
            console.log(`    ❌ Missing import: ${importName}`);
            this.issues.push(`Missing import in setup: ${importName}`);
            importOrderCorrect = false;
          } else if (index < lastIndex) {
            console.log(`    ⚠️  Import order issue: ${importName}`);
            this.issues.push(`Import order issue: ${importName} should come before previous imports`);
            importOrderCorrect = false;
          } else {
            console.log(`    ✅ ${importName}: Correct order`);
            lastIndex = index;
          }
        }

        // Check for polyfills
        if (setupContent.includes('isTypedArray')) {
          console.log('  ✅ TypedArray polyfills present');
        } else {
          console.log('  ⚠️  TypedArray polyfills missing');
          this.recommendations.push('Add TypedArray polyfills for React Native compatibility');
        }

        // Check for tf.ready() call
        if (setupContent.includes('tf.ready()')) {
          console.log('  ✅ tf.ready() initialization present');
        } else {
          console.log('  ❌ tf.ready() initialization missing');
          this.issues.push('Missing tf.ready() initialization');
        }

        this.diagnosticResults.setup = importOrderCorrect && setupContent.includes('tf.ready()');
      } else {
        console.log('  ❌ TensorFlow.js setup file missing');
        this.issues.push('TensorFlow.js setup file missing');
        this.diagnosticResults.setup = false;
      }

      console.log(`\n📊 Setup Configuration: ${this.diagnosticResults.setup ? 'PASS' : 'FAIL'}\n`);

    } catch (error) {
      console.log(`❌ Failed to analyze setup: ${error.message}\n`);
      this.issues.push(`Failed to analyze setup: ${error.message}`);
    }
  }

  async checkModelFiles() {
    console.log('🔍 Test 3: Checking Model Files');
    console.log('===============================');
    
    try {
      const modelPaths = [
        'ai-models/FootFit_Arch_Height_CNN.h5',
        'ai-models/tfjs_model/model.json',
        'ai-models/tfjs_model/weights.bin'
      ];

      let modelFilesFound = 0;
      
      console.log('  📁 Model file availability:');
      for (const modelPath of modelPaths) {
        if (fs.existsSync(modelPath)) {
          console.log(`    ✅ ${modelPath}: Found`);
          modelFilesFound++;
        } else {
          console.log(`    ❌ ${modelPath}: Missing`);
          if (modelPath.includes('tfjs_model')) {
            this.issues.push(`Missing TensorFlow.js model file: ${modelPath}`);
          }
        }
      }

      // Check if tfjs_model directory exists
      const tfjsModelDir = 'ai-models/tfjs_model';
      if (fs.existsSync(tfjsModelDir)) {
        console.log('  ✅ TensorFlow.js model directory exists');
        
        const files = fs.readdirSync(tfjsModelDir);
        console.log(`    📋 Files in tfjs_model: ${files.join(', ')}`);
        
        if (files.includes('model.json')) {
          console.log('    ✅ model.json found');
        } else {
          console.log('    ❌ model.json missing');
          this.issues.push('model.json missing from tfjs_model directory');
        }
      } else {
        console.log('  ❌ TensorFlow.js model directory missing');
        this.issues.push('TensorFlow.js model directory missing - need to convert .h5 model');
        this.recommendations.push('Convert .h5 model to TensorFlow.js format using tensorflowjs_converter');
      }

      // Check integration guide
      const integrationGuide = 'ai-models/INTEGRATION_GUIDE.md';
      if (fs.existsSync(integrationGuide)) {
        console.log('  ✅ Integration guide available');
      } else {
        console.log('  ⚠️  Integration guide missing');
      }

      this.diagnosticResults.modelFiles = fs.existsSync(tfjsModelDir) && fs.existsSync('ai-models/tfjs_model/model.json');
      console.log(`\n📊 Model Files: ${this.diagnosticResults.modelFiles ? 'PASS' : 'FAIL'}\n`);

    } catch (error) {
      console.log(`❌ Failed to check model files: ${error.message}\n`);
      this.issues.push(`Failed to check model files: ${error.message}`);
    }
  }

  async testCompatibility() {
    console.log('🔍 Test 4: Testing Compatibility');
    console.log('================================');
    
    try {
      // Check React Native version
      const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
      const reactVersion = packageJson.dependencies?.react;
      const expoVersion = packageJson.dependencies?.expo;
      
      console.log('  📱 Platform compatibility:');
      console.log(`    React: ${reactVersion || 'Unknown'}`);
      console.log(`    Expo: ${expoVersion || 'Unknown'}`);
      
      // Check for known compatibility issues
      const tfVersion = packageJson.dependencies?.['@tensorflow/tfjs'];
      console.log(`    TensorFlow.js: ${tfVersion || 'Unknown'}`);
      
      if (tfVersion && tfVersion.includes('4.13.0')) {
        console.log('    ✅ TensorFlow.js version compatible with React Native');
      } else {
        console.log('    ⚠️  TensorFlow.js version may have compatibility issues');
        this.recommendations.push('Use TensorFlow.js version 4.13.0 for best React Native compatibility');
      }

      // Check for Metro configuration
      const metroConfig = 'metro.config.js';
      if (fs.existsSync(metroConfig)) {
        console.log('  ✅ Metro configuration exists');
        
        const metroContent = fs.readFileSync(metroConfig, 'utf8');
        if (metroContent.includes('assetExts') && metroContent.includes('bin')) {
          console.log('    ✅ Metro configured for .bin files (TensorFlow.js models)');
        } else {
          console.log('    ⚠️  Metro may not be configured for .bin files');
          this.recommendations.push('Configure Metro to handle .bin files for TensorFlow.js models');
        }
      } else {
        console.log('  ⚠️  Metro configuration missing');
        this.recommendations.push('Add Metro configuration for TensorFlow.js compatibility');
      }

      this.diagnosticResults.compatibility = true; // Basic compatibility check
      console.log(`\n📊 Compatibility: ${this.diagnosticResults.compatibility ? 'PASS' : 'FAIL'}\n`);

    } catch (error) {
      console.log(`❌ Failed to test compatibility: ${error.message}\n`);
      this.issues.push(`Failed to test compatibility: ${error.message}`);
    }
  }

  generateDiagnosticReport() {
    console.log('📋 TENSORFLOW.JS DIAGNOSTIC REPORT');
    console.log('===================================\n');

    // Calculate overall score
    const tests = Object.keys(this.diagnosticResults).filter(key => key !== 'overallScore');
    const passedTests = tests.filter(test => this.diagnosticResults[test]).length;
    this.diagnosticResults.overallScore = Math.round((passedTests / tests.length) * 100);

    console.log('📊 TEST RESULTS:');
    console.log(`  Dependencies: ${this.diagnosticResults.dependencies ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  Setup Configuration: ${this.diagnosticResults.setup ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  Model Files: ${this.diagnosticResults.modelFiles ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  Compatibility: ${this.diagnosticResults.compatibility ? '✅ PASS' : '❌ FAIL'}`);

    console.log(`\n🏆 OVERALL SCORE: ${this.diagnosticResults.overallScore}%`);

    if (this.diagnosticResults.overallScore >= 75) {
      console.log('✅ TensorFlow.js setup is mostly ready');
    } else {
      console.log('⚠️  TensorFlow.js setup needs attention');
    }

    if (this.issues.length > 0) {
      console.log('\n❌ ISSUES IDENTIFIED:');
      this.issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue}`);
      });
    }

    if (this.recommendations.length > 0) {
      console.log('\n💡 RECOMMENDATIONS:');
      this.recommendations.forEach((rec, index) => {
        console.log(`  ${index + 1}. ${rec}`);
      });
    }

    console.log('\n🚀 NEXT STEPS:');
    if (this.diagnosticResults.overallScore < 75) {
      console.log('  1. Address the identified issues');
      console.log('  2. Follow the recommendations');
      console.log('  3. Re-run diagnostics');
    } else {
      console.log('  1. Test CNN model loading');
      console.log('  2. Validate foot analysis pipeline');
      console.log('  3. Test end-to-end functionality');
    }

    console.log('\n🎓 ACADEMIC PROJECT STATUS:');
    if (this.diagnosticResults.modelFiles) {
      console.log('  ✅ Model files available for CNN integration');
    } else {
      console.log('  ❌ Model files missing - need to convert from Colab');
    }
    
    if (this.diagnosticResults.setup) {
      console.log('  ✅ TensorFlow.js setup configured');
    } else {
      console.log('  ❌ TensorFlow.js setup needs configuration');
    }
  }
}

async function main() {
  try {
    const diagnostics = new TensorFlowDiagnostics();
    const results = await diagnostics.runDiagnostics();
    
    console.log('\n🎉 TENSORFLOW.JS DIAGNOSTICS COMPLETE');
    console.log('=====================================');
    console.log(`Overall readiness: ${results.overallScore}%`);
    
  } catch (error) {
    console.error('❌ Diagnostics failed:', error.message);
    process.exit(1);
  }
}

main();
