# FootFit Shoe Database Population Guide

## Overview
This guide provides instructions for populating the FootFit shoe database with 150+ comprehensive shoe models across 29 brands and 5 categories.

## Problem Solved
- **RLS Policy Violations**: The scripts temporarily disable Row Level Security for data insertion
- **Incomplete Database**: Populates database with real shoe models instead of relying on mock data fallbacks
- **Brand Diversity**: Creates professional-level brand coverage comparable to real shoe retail platforms

## Files Required
1. `comprehensive_shoe_population.sql` - Main script (Tier 1 & 2 brands)
2. `comprehensive_shoe_population_part2.sql` - Continuation script (Tier 3 brands)

## Execution Instructions

### Method 1: Supabase SQL Editor (Recommended for Academic Projects)

1. **Open Supabase Dashboard**
   - Go to your Supabase project dashboard
   - Navigate to SQL Editor

2. **Execute Part 1**
   - Copy the entire content of `comprehensive_shoe_population.sql`
   - Paste into SQL Editor
   - Click "Run" to execute
   - Verify no errors in the output

3. **Execute Part 2**
   - Copy the entire content of `comprehensive_shoe_population_part2.sql`
   - Paste into SQL Editor
   - Click "Run" to execute
   - Review the verification output

### Method 2: Command Line (Alternative)

```bash
# If you have psql access to your Supabase database
psql "postgresql://postgres:[password]@[host]:5432/postgres" -f comprehensive_shoe_population.sql
psql "postgresql://postgres:[password]@[host]:5432/postgres" -f comprehensive_shoe_population_part2.sql
```

## Expected Results

### Database Population Summary
- **Categories**: 5 (Performance Sports, Outdoor & Hiking, Everyday Casual, Dress & Formal, Specialty Comfort)
- **Brands**: 29 total brands
- **Models**: 150+ shoe models
- **Brand-Category Mappings**: Comprehensive cross-category coverage

### Category Distribution Target
- **Performance Sports**: 35+ models
- **Outdoor & Hiking**: 30+ models  
- **Everyday Casual**: 40+ models
- **Dress & Formal**: 25+ models
- **Specialty Comfort**: 20+ models

### Brand Tier Distribution
- **Tier 1 Brands** (8+ models each): Nike, Adidas, New Balance, ASICS, Brooks, Puma
- **Tier 2 Brands** (5-7 models each): Hoka, Vans, Converse, Merrell, Salomon, Skechers
- **Tier 3 Brands** (3-4 models each): Under Armour, Jordan, Reebok, The North Face, KEEN, Columbia, Allbirds, Birkenstock, Dansko, OOFOS, Cole Haan, Clarks, Johnston & Murphy, Gucci, Louis Vuitton, Balenciaga, Timberland, Dr. Martens

## Verification

After execution, the scripts will output:
1. **Population Summary**: Total records created in each table
2. **Category Analysis**: Model count and price ranges per category
3. **Brand Analysis**: Model count and category coverage per brand
4. **Success Message**: Confirmation of total models populated

## Security Notes

- Scripts temporarily disable RLS for data insertion
- RLS is automatically re-enabled after population
- Only run in development/academic environments
- Admin privileges required in Supabase SQL Editor

## Troubleshooting

### Common Issues
1. **Permission Denied**: Ensure you're using Supabase SQL Editor with admin access
2. **RLS Violations**: Scripts handle this by temporarily disabling RLS
3. **Timeout**: Large scripts may take 30-60 seconds to complete

### Verification Queries
```sql
-- Check total models
SELECT COUNT(*) as total_models FROM shoe_models;

-- Check category distribution
SELECT c.name, COUNT(m.id) as model_count 
FROM shoe_categories c 
LEFT JOIN shoe_models m ON c.id = m.category_id 
GROUP BY c.name;

-- Check brand coverage
SELECT b.name, COUNT(m.id) as model_count 
FROM shoe_brands b 
LEFT JOIN shoe_models m ON b.id = m.brand_id 
GROUP BY b.name 
ORDER BY model_count DESC;
```

## Academic Project Benefits

- **Professional Presentation**: Database rivals commercial shoe platforms
- **Real Data Integration**: Eliminates mock data fallbacks in the application
- **Comprehensive Coverage**: All filtering combinations return meaningful results
- **Scalable Architecture**: Supports future expansion and modifications
- **Academic Credibility**: Demonstrates real database design and population skills

## Next Steps

After successful population:
1. Test the FootFit application's recommendation system
2. Verify category and brand filtering functionality
3. Confirm image URLs are accessible
4. Test search and recommendation algorithms with real data
5. Prepare for academic demonstrations with diverse shoe options
