import { ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Card, CardContent } from '@/components/ui/Card';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useTheme } from '@/contexts/ThemeContext';

export default function TutorialScreen() {
  const { colors } = useTheme();

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <ThemedView style={styles.header}>
        <View style={styles.headerContent}>
          <View style={styles.logoContainer}>
            <View style={[styles.logo, { backgroundColor: colors.primary }]}>
              <IconSymbol
                size={32}
                name="questionmark.circle.fill"
                color={colors.textInverse}
              />
            </View>
            <ThemedText variant="h2" color="primary">Tutorial</ThemedText>
          </View>
        </View>
      </ThemedView>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Learn How To Section */}
        <View style={styles.learnHowToSection}>
          <ThemedText variant="body" color="secondary" style={styles.learnHowToText}>
            Learn how to get the best measurements
          </ThemedText>
        </View>
        {/* How it Works Section */}
        <View style={styles.section}>
          <ThemedText variant="h3" style={styles.sectionTitle}>
            How it works
          </ThemedText>

          <View style={styles.cardGrid}>
            <Card variant="outlined" padding="large">
              <CardContent>
                <View style={styles.cardHeader}>
                  <View style={[styles.stepIcon, { backgroundColor: colors.primary + '20' }]}>
                    <ThemedText variant="h4" color="primary">1</ThemedText>
                  </View>
                  <ThemedText variant="labelLarge">Take a Photo</ThemedText>
                </View>
                <ThemedText variant="bodySmall" color="secondary">
                  Capture a top-view image of your foot on a flat surface
                </ThemedText>
              </CardContent>
            </Card>

            <Card variant="outlined" padding="large">
              <CardContent>
                <View style={styles.cardHeader}>
                  <View style={[styles.stepIcon, { backgroundColor: colors.primary + '20' }]}>
                    <ThemedText variant="h4" color="primary">2</ThemedText>
                  </View>
                  <ThemedText variant="labelLarge">AI Analysis</ThemedText>
                </View>
                <ThemedText variant="bodySmall" color="secondary">
                  Our AI measures length and width accurately using computer vision
                </ThemedText>
              </CardContent>
            </Card>

            <Card variant="outlined" padding="large">
              <CardContent>
                <View style={styles.cardHeader}>
                  <View style={[styles.stepIcon, { backgroundColor: colors.primary + '20' }]}>
                    <ThemedText variant="h4" color="primary">3</ThemedText>
                  </View>
                  <ThemedText variant="labelLarge">Get Recommendations</ThemedText>
                </View>
                <ThemedText variant="bodySmall" color="secondary">
                  Receive personalized shoe size and style suggestions
                </ThemedText>
              </CardContent>
            </Card>
          </View>
        </View>

        {/* Photo Taking Guide */}
        <View style={styles.section}>
          <ThemedText variant="h3" style={styles.sectionTitle}>
            Photo Taking Guide
          </ThemedText>

          <View style={styles.cardGrid}>
            <Card variant="outlined" padding="medium">
              <CardContent>
                <View style={styles.cardHeader}>
                  <IconSymbol size={24} name="lightbulb.fill" color={colors.primary} />
                  <ThemedText variant="labelLarge">Good Lighting</ThemedText>
                </View>
                <ThemedText variant="bodySmall" color="secondary">
                  Use bright, even lighting. Natural daylight works best. Avoid shadows on your foot.
                </ThemedText>
              </CardContent>
            </Card>

            <Card variant="outlined" padding="medium">
              <CardContent>
                <View style={styles.cardHeader}>
                  <IconSymbol size={24} name="viewfinder" color={colors.primary} />
                  <ThemedText variant="labelLarge">Proper Framing</ThemedText>
                </View>
                <ThemedText variant="bodySmall" color="secondary">
                  Ensure your entire foot is visible in the frame with some space around the edges.
                </ThemedText>
              </CardContent>
            </Card>

            <Card variant="outlined" padding="medium">
              <CardContent>
                <View style={styles.cardHeader}>
                  <IconSymbol size={24} name="ruler" color={colors.primary} />
                  <ThemedText variant="labelLarge">Flat Surface</ThemedText>
                </View>
                <ThemedText variant="bodySmall" color="secondary">
                  Place your foot on a flat surface with good contrast (light foot on dark surface or vice versa).
                </ThemedText>
              </CardContent>
            </Card>

            <Card variant="outlined" padding="medium">
              <CardContent>
                <View style={styles.cardHeader}>
                  <IconSymbol size={24} name="camera.viewfinder" color={colors.primary} />
                  <ThemedText variant="labelLarge">Camera Angle</ThemedText>
                </View>
                <ThemedText variant="bodySmall" color="secondary">
                  Hold the camera directly above your foot, parallel to the ground for accurate measurements.
                </ThemedText>
              </CardContent>
            </Card>

            <Card variant="outlined" padding="medium">
              <CardContent>
                <View style={styles.cardHeader}>
                  <IconSymbol size={24} name="figure.stand" color={colors.primary} />
                  <ThemedText variant="labelLarge">Foot Position</ThemedText>
                </View>
                <ThemedText variant="bodySmall" color="secondary">
                  Stand naturally with your weight evenly distributed. Don&apos;t curl your toes or arch your foot.
                </ThemedText>
              </CardContent>
            </Card>
          </View>
        </View>

        {/* Tips for Best Results */}
        <View style={styles.section}>
          <ThemedText variant="h3" style={styles.sectionTitle}>
            Tips for Best Results
          </ThemedText>

          <View style={styles.tipsList}>
            <View style={styles.tipItem}>
              <IconSymbol size={20} name="checkmark.circle.fill" color={colors.primary} />
              <ThemedText variant="body" style={styles.tipText}>
                Measure both feet - they can differ in size
              </ThemedText>
            </View>

            <View style={styles.tipItem}>
              <IconSymbol size={20} name="checkmark.circle.fill" color={colors.primary} />
              <ThemedText variant="body" style={styles.tipText}>
                Take measurements at the end of the day when feet are largest
              </ThemedText>
            </View>

            <View style={styles.tipItem}>
              <IconSymbol size={20} name="checkmark.circle.fill" color={colors.primary} />
              <ThemedText variant="body" style={styles.tipText}>
                Wear the type of socks you&apos;ll wear with the shoes
              </ThemedText>
            </View>

            <View style={styles.tipItem}>
              <IconSymbol size={20} name="checkmark.circle.fill" color={colors.primary} />
              <ThemedText variant="body" style={styles.tipText}>
                Retake the photo if the AI confidence is below 85%
              </ThemedText>
            </View>

            <View style={styles.tipItem}>
              <IconSymbol size={20} name="checkmark.circle.fill" color={colors.primary} />
              <ThemedText variant="body" style={styles.tipText}>
                Use a reference object (like a coin) for scale if available
              </ThemedText>
            </View>
          </View>
        </View>

        {/* Troubleshooting */}
        <View style={[styles.section, styles.lastSection]}>
          <ThemedText variant="h3" style={styles.sectionTitle}>
            Troubleshooting
          </ThemedText>

          <View style={styles.troubleshootList}>
            <View style={styles.troubleshootItem}>
              <ThemedText variant="labelLarge" color="primary">
                Low confidence score?
              </ThemedText>
              <ThemedText variant="bodySmall" color="secondary">
                Try better lighting, ensure full foot visibility, and check surface contrast.
              </ThemedText>
            </View>

            <View style={styles.troubleshootItem}>
              <ThemedText variant="labelLarge" color="primary">
                Measurements seem off?
              </ThemedText>
              <ThemedText variant="bodySmall" color="secondary">
                Verify camera angle is perpendicular to the ground and foot is flat.
              </ThemedText>
            </View>

            <View style={styles.troubleshootItem}>
              <ThemedText variant="labelLarge" color="primary">
                App not working?
              </ThemedText>
              <ThemedText variant="bodySmall" color="secondary">
                Check camera permissions and ensure good internet connection for AI processing.
              </ThemedText>
            </View>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 20,
  },
  headerContent: {
    gap: 8,
  },
  logoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  logo: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scrollView: {
    flex: 1,
  },
  learnHowToSection: {
    paddingHorizontal: 20,
    paddingTop: 16,
    paddingBottom: 8,
  },
  learnHowToText: {
    textAlign: 'center',
  },
  section: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  lastSection: {
    paddingBottom: 40,
  },
  sectionTitle: {
    marginBottom: 20,
    textAlign: 'center',
  },
  cardGrid: {
    gap: 16,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 8,
  },
  stepsList: {
    gap: 20,
  },
  stepItem: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  stepIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
  },
  stepText: {
    flex: 1,
    gap: 4,
  },
  guideList: {
    gap: 20,
  },
  guideItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 16,
  },
  guideIcon: {
    width: 48,
    height: 48,
    borderRadius: 24,
    alignItems: 'center',
    justifyContent: 'center',
    marginTop: 2,
  },
  guideText: {
    flex: 1,
    gap: 4,
  },
  tipsList: {
    gap: 16,
  },
  tipItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    gap: 12,
  },
  tipText: {
    flex: 1,
    lineHeight: 20,
  },
  troubleshootList: {
    gap: 16,
  },
  troubleshootItem: {
    gap: 4,
  },
});
