{"format": "layers-model", "generatedBy": "keras v2.13.1", "convertedBy": "TensorFlow.js Converter v4.13.0", "modelTopology": {"keras_version": "2.13.1", "backend": "tensorflow", "model_config": {"class_name": "Sequential", "config": {"name": "FootFit_Arch_Height_CNN", "layers": [{"class_name": "InputLayer", "config": {"batch_input_shape": [null, 224, 224, 3], "dtype": "float32", "sparse": false, "ragged": false, "name": "input_1"}}, {"class_name": "Conv2D", "config": {"name": "conv2d_1", "trainable": true, "filters": 32, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "activation": "relu", "use_bias": true}}, {"class_name": "MaxPooling2D", "config": {"name": "max_pooling2d_1", "trainable": true, "pool_size": [2, 2], "padding": "valid", "strides": [2, 2]}}, {"class_name": "Conv2D", "config": {"name": "conv2d_2", "trainable": true, "filters": 64, "kernel_size": [3, 3], "strides": [1, 1], "padding": "same", "activation": "relu", "use_bias": true}}, {"class_name": "MaxPooling2D", "config": {"name": "max_pooling2d_2", "trainable": true, "pool_size": [2, 2], "padding": "valid", "strides": [2, 2]}}, {"class_name": "<PERSON><PERSON>", "config": {"name": "flatten_1", "trainable": true}}, {"class_name": "<PERSON><PERSON>", "config": {"name": "dense_1", "trainable": true, "units": 128, "activation": "relu", "use_bias": true}}, {"class_name": "<PERSON><PERSON>", "config": {"name": "dense_2", "trainable": true, "units": 3, "activation": "linear", "use_bias": true}}]}}, "training_config": {"loss": "mse", "metrics": ["mae"], "optimizer_config": {"class_name": "<PERSON>", "config": {"learning_rate": 0.001, "beta_1": 0.9, "beta_2": 0.999, "epsilon": 1e-07}}}}, "weightsManifest": [{"paths": ["weights.bin"], "weights": [{"name": "conv2d_1/kernel", "shape": [3, 3, 3, 32], "dtype": "float32"}, {"name": "conv2d_1/bias", "shape": [32], "dtype": "float32"}, {"name": "conv2d_2/kernel", "shape": [3, 3, 32, 64], "dtype": "float32"}, {"name": "conv2d_2/bias", "shape": [64], "dtype": "float32"}, {"name": "dense_1/kernel", "shape": [50176, 128], "dtype": "float32"}, {"name": "dense_1/bias", "shape": [128], "dtype": "float32"}, {"name": "dense_2/kernel", "shape": [128, 3], "dtype": "float32"}, {"name": "dense_2/bias", "shape": [3], "dtype": "float32"}]}]}