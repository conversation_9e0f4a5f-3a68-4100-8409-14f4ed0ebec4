/**
 * FootFit AI Service Types (Legacy)
 *
 * This file is kept for backward compatibility.
 * New code should import types from './types.ts'
 *
 * @deprecated Use './types.ts' instead
 */

// Re-export types from the consolidated types file
export type {
    FootMeasurement, MeasurementRequest,
    MeasurementResponse, ShoeRecommendation
} from './types';

// Import types for internal use

// Mock data removed - using real Supabase database only

// Simulate processing delay
const PROCESSING_DELAY_MS = 2000; // 2 seconds

// Mock measurement generation removed - using real AI analysis only

// Legacy MockAIService removed - use FootAnalysisAI from './footAnalysisAI' instead
