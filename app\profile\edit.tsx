import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import { useEffect, useState } from 'react';
import { Alert, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingScreen } from '@/components/ui/Loading';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';

export default function EditProfileScreen() {
  const { colors } = useTheme();
  const { user, profile, updateProfile, loading } = useAuth();
  
  const [formData, setFormData] = useState({
    full_name: '',
    preferred_unit: 'cm' as 'cm' | 'inches',
    preferred_brands: [] as string[],
  });
  const [saving, setSaving] = useState(false);
  const [availableBrands, setAvailableBrands] = useState<string[]>([]);
  const [loadingBrands, setLoadingBrands] = useState(true);

  // Load dynamic brands from database
  const loadBrands = async () => {
    try {
      setLoadingBrands(true);
      // Import function dynamically to avoid import issues
      const { getShoeBrands } = await import('@/services/shoeDataService');
      const brands = await getShoeBrands();

      // Extract brand names and sort alphabetically
      const brandNames = brands.map(brand => brand.name).sort();
      setAvailableBrands(brandNames);
    } catch (error) {
      console.error('Error loading brands:', error);
      // Fallback to hardcoded brands
      setAvailableBrands(['Nike', 'Adidas', 'Converse', 'Vans', 'New Balance', 'Puma', 'Reebok']);
    } finally {
      setLoadingBrands(false);
    }
  };

  // Initialize form data
  useEffect(() => {
    if (profile) {
      setFormData({
        full_name: profile.full_name || '',
        preferred_unit: profile.preferred_unit || 'cm',
        preferred_brands: profile.preferred_brands || [],
      });
    }
  }, [profile]);

  // Load brands on component mount
  useEffect(() => {
    loadBrands();
  }, []);

  const handleSave = async () => {
    if (!formData.full_name.trim()) {
      Alert.alert('Validation Error', 'Please enter your full name.');
      return;
    }

    try {
      setSaving(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      const { error } = await updateProfile(formData);
      
      if (error) {
        throw error;
      }

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      Alert.alert(
        'Profile Updated',
        'Your profile has been updated successfully.',
        [
          {
            text: 'OK',
            onPress: () => router.back()
          }
        ]
      );
    } catch {
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      Alert.alert('Error', 'Failed to update profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const toggleBrand = (brand: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);

    setFormData(prev => {
      const isSelected = prev.preferred_brands.includes(brand);

      if (isSelected) {
        // Remove brand if already selected
        return {
          ...prev,
          preferred_brands: prev.preferred_brands.filter(b => b !== brand)
        };
      } else {
        // Check if we can add more brands (limit to 5)
        if (prev.preferred_brands.length >= 5) {
          Haptics.notificationAsync(Haptics.NotificationFeedbackType.Warning);
          Alert.alert(
            'Maximum Brands Selected',
            'You can select up to 5 favorite brands. Please remove one to add another.',
            [{ text: 'OK' }]
          );
          return prev;
        }

        // Add brand
        return {
          ...prev,
          preferred_brands: [...prev.preferred_brands, brand]
        };
      }
    });
  };

  if (loading) {
    return <LoadingScreen text="Loading profile..." />;
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <ThemedView style={styles.header}>
        <Button
          title=""
          onPress={() => {
            Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
            router.back();
          }}
          variant="ghost"
          size="small"
          icon="chevron.left"
          style={styles.backButton}
        />
        <ThemedText variant="h3">Edit Profile</ThemedText>
        <Button
          title={saving ? 'Saving...' : 'Save'}
          onPress={handleSave}
          variant={saving ? "ghost" : "primary"}
          size="small"
          disabled={saving}
          style={styles.saveButton}
        />
      </ThemedView>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Personal Information */}
        <ThemedView style={styles.section}>
          <ThemedText variant="h4" style={styles.sectionTitle}>
            Personal Information
          </ThemedText>
          
          <ThemedView style={[styles.card, { borderColor: colors.border }]}>
            <View style={styles.inputGroup}>
              <ThemedText variant="labelLarge" style={styles.inputLabel}>
                Full Name
              </ThemedText>
              <View style={[styles.textInput, { borderColor: colors.border, backgroundColor: colors.backgroundSecondary }]}>
                <ThemedText 
                  variant="body" 
                  style={styles.inputText}
                  onPress={() => {
                    Alert.prompt(
                      'Edit Full Name',
                      'Enter your full name:',
                      (text) => {
                        if (text !== null) {
                          setFormData(prev => ({ ...prev, full_name: text }));
                        }
                      },
                      'plain-text',
                      formData.full_name
                    );
                  }}
                >
                  {formData.full_name || 'Tap to enter your name'}
                </ThemedText>
                <IconSymbol size={20} name="pencil" color={colors.iconSecondary} />
              </View>
            </View>

            <View style={styles.inputGroup}>
              <ThemedText variant="labelLarge" style={styles.inputLabel}>
                Email Address
              </ThemedText>
              <View style={[styles.textInput, { borderColor: colors.border, backgroundColor: colors.backgroundTertiary }]}>
                <ThemedText variant="body" color="secondary" style={styles.inputText}>
                  {user?.email || 'No email'}
                </ThemedText>
                <IconSymbol size={20} name="lock.fill" color={colors.iconSecondary} />
              </View>
              <ThemedText variant="caption" color="tertiary" style={styles.inputHint}>
                Email cannot be changed from this screen
              </ThemedText>
            </View>
          </ThemedView>
        </ThemedView>

        {/* Measurement Preferences */}
        <ThemedView style={styles.section}>
          <ThemedText variant="h4" style={styles.sectionTitle}>
            Measurement Preferences
          </ThemedText>
          
          <ThemedView style={[styles.card, { borderColor: colors.border }]}>
            <View style={styles.inputGroup}>
              <ThemedText variant="labelLarge" style={styles.inputLabel}>
                Preferred Unit
              </ThemedText>
              <View style={styles.unitSelector}>
                {['cm', 'inches'].map((unit) => (
                  <TouchableOpacity
                    key={unit}
                    style={[
                      styles.unitOption,
                      {
                        backgroundColor: formData.preferred_unit === unit ? colors.primary : colors.backgroundSecondary,
                        borderColor: formData.preferred_unit === unit ? colors.primary : colors.border,
                      }
                    ]}
                    onPress={() => {
                      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                      setFormData(prev => ({ ...prev, preferred_unit: unit as 'cm' | 'inches' }));
                    }}
                    activeOpacity={0.7}
                  >
                    <ThemedText
                      variant="labelLarge"
                      color={formData.preferred_unit === unit ? 'inverse' : 'primary'}
                    >
                      {unit === 'cm' ? 'Centimeters' : 'Inches'}
                    </ThemedText>
                  </TouchableOpacity>
                ))}
              </View>
            </View>
          </ThemedView>
        </ThemedView>

        {/* Favorite Brands */}
        <ThemedView style={styles.section}>
          <View style={styles.brandSectionHeader}>
            <ThemedText variant="h4" style={styles.sectionTitle}>
              Favorite Shoe Brands
            </ThemedText>
            <View style={styles.brandCounter}>
              <ThemedText variant="caption" color="secondary">
                {formData.preferred_brands.length}/5 selected
              </ThemedText>
            </View>
          </View>

          <ThemedView style={[styles.card, { borderColor: colors.border }]}>
            <ThemedText variant="body" color="secondary" style={styles.sectionDescription}>
              Select up to 5 favorite brands to get personalized recommendations
            </ThemedText>

            {loadingBrands ? (
              <View style={styles.loadingBrands}>
                <ThemedText variant="body" color="secondary">
                  Loading brands from database...
                </ThemedText>
              </View>
            ) : (
              <View style={styles.brandsGrid}>
                {availableBrands.map((brand) => {
                  const isSelected = formData.preferred_brands.includes(brand);
                  const isDisabled = !isSelected && formData.preferred_brands.length >= 5;

                  return (
                    <TouchableOpacity
                      key={brand}
                      style={[
                        styles.brandChip,
                        {
                          backgroundColor: isSelected ? colors.primary : colors.backgroundSecondary,
                          borderColor: isSelected ? colors.primary : colors.border,
                          opacity: isDisabled ? 0.5 : 1,
                        }
                      ]}
                      onPress={() => toggleBrand(brand)}
                      disabled={isDisabled}
                      activeOpacity={0.7}
                    >
                      <ThemedText
                        variant="labelLarge"
                        color={isSelected ? 'inverse' : 'primary'}
                      >
                        {brand}
                      </ThemedText>
                      {isSelected && (
                        <IconSymbol
                          name="checkmark"
                          size={14}
                          color={colors.textInverse}
                        />
                      )}
                    </TouchableOpacity>
                  );
                })}
              </View>
            )}
          </ThemedView>
        </ThemedView>

        {/* Bottom spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    width: 40,
  },
  saveButton: {
    width: 80,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    paddingBottom: 24,
  },
  sectionTitle: {
    marginBottom: 16,
  },
  sectionDescription: {
    marginBottom: 16,
    lineHeight: 20,
  },
  card: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 20,
    gap: 20,
  },
  inputGroup: {
    gap: 8,
  },
  inputLabel: {
    marginBottom: 4,
  },
  textInput: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 12,
    borderRadius: 8,
    borderWidth: 1,
  },
  inputText: {
    flex: 1,
  },
  inputHint: {
    marginTop: 4,
  },
  unitSelector: {
    flexDirection: 'row',
    gap: 12,
  },
  unitOption: {
    flex: 1,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 8,
    borderWidth: 1,
    alignItems: 'center',
  },
  brandsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  brandChip: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 6,
    paddingHorizontal: 16,
    paddingVertical: 8,
    borderRadius: 20,
    borderWidth: 1,
  },
  brandSectionHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 16,
  },
  brandCounter: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.05)',
  },
  loadingBrands: {
    paddingVertical: 20,
    alignItems: 'center',
  },
  bottomSpacing: {
    height: 40,
  },
});
