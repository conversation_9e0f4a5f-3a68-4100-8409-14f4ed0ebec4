const { getDefaultConfig } = require('expo/metro-config');
const path = require('path');

/** @type {import('expo/metro-config').MetroConfig} */
const config = getDefaultConfig(__dirname);

// Add support for TensorFlow.js and resolve bundling issues
config.resolver.alias = {
  ...config.resolver.alias,
  // Add @ alias for project root
  '@': path.resolve(__dirname, '.'),
};

// Add file extensions for TensorFlow.js
config.resolver.sourceExts = [
  ...config.resolver.sourceExts,
  'bin', // For TensorFlow.js model weights
];

// Configure asset extensions for FootFit assets
config.resolver.assetExts = [
  ...config.resolver.assetExts,
  // Standard image and media formats are already included
];

// Configure transformer to handle TensorFlow.js modules
config.transformer = {
  ...config.transformer,
  getTransformOptions: async () => ({
    transform: {
      experimentalImportSupport: false,
      inlineRequires: true,
    },
  }),
};

// Resolve module resolution issues
config.resolver.resolverMainFields = ['react-native', 'browser', 'main'];

// Configure Metro to handle large bundles (for TensorFlow.js)
config.serializer = {
  ...config.serializer,
  customSerializer: undefined,
};

// Remove problematic resolver that was causing issues

// Configure watchman to ignore large directories
config.watchFolders = [
  path.resolve(__dirname, 'node_modules'),
  path.resolve(__dirname, 'assets'),
  path.resolve(__dirname, 'datasets'),
];

// Ignore problematic files that cause bundling issues
config.resolver.blacklistRE = /node_modules\/.*\/node_modules\/react-native\/.*/;

module.exports = config;
