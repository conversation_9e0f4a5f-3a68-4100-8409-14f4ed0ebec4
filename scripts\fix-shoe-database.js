/**
 * <PERSON><PERSON>t to fix the existing Supabase shoe database
 * Adds missing size_range data to existing shoe models
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Load environment variables
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function fixShoeDatabase() {
  try {
    console.log('🔧 Starting shoe database fix...');

    // Standard size range for all shoes
    const standardSizeRange = {
      min_uk: 6,
      max_uk: 13,
      available_sizes: [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]
    };

    // Get all shoe models that need size_range data
    console.log('📋 Fetching existing shoe models...');
    const { data: models, error: fetchError } = await supabase
      .from('shoe_models')
      .select('id, model_name, size_range')
      .is('size_range', null);

    if (fetchError) {
      console.error('Error fetching models:', fetchError);
      throw fetchError;
    }

    console.log(`Found ${models.length} models without size_range data`);

    if (models.length === 0) {
      console.log('✅ All models already have size_range data!');
      return;
    }

    // Update models in batches
    const batchSize = 10;
    let updatedCount = 0;

    for (let i = 0; i < models.length; i += batchSize) {
      const batch = models.slice(i, i + batchSize);
      
      console.log(`🔄 Updating batch ${Math.floor(i / batchSize) + 1}/${Math.ceil(models.length / batchSize)}...`);
      
      for (const model of batch) {
        const { error: updateError } = await supabase
          .from('shoe_models')
          .update({ size_range: standardSizeRange })
          .eq('id', model.id);

        if (updateError) {
          console.error(`Error updating model ${model.model_name}:`, updateError);
        } else {
          updatedCount++;
          console.log(`  ✅ Updated: ${model.model_name}`);
        }
      }
    }

    console.log(`✅ Successfully updated ${updatedCount} shoe models with size_range data`);

    // Verify the fix
    console.log('🔍 Verifying the fix...');
    const { data: verifyModels, error: verifyError } = await supabase
      .from('shoe_models')
      .select('id, model_name, size_range')
      .is('size_range', null);

    if (verifyError) {
      console.error('Error verifying fix:', verifyError);
    } else {
      console.log(`Remaining models without size_range: ${verifyModels.length}`);
      if (verifyModels.length === 0) {
        console.log('🎉 All models now have size_range data!');
      }
    }

    // Test the recommendation system
    console.log('🧪 Testing shoe recommendations...');
    const { data: categories } = await supabase.from('shoe_categories').select('*');
    const { data: brands } = await supabase.from('shoe_brands').select('*');
    const { data: allModels } = await supabase.from('shoe_models').select('*');
    const { data: mappings } = await supabase.from('brand_category_mapping').select('*');

    console.log('📊 Database summary:');
    console.log(`  Categories: ${categories?.length || 0}`);
    console.log(`  Brands: ${brands?.length || 0}`);
    console.log(`  Models: ${allModels?.length || 0}`);
    console.log(`  Mappings: ${mappings?.length || 0}`);

    if (categories) {
      console.log('  Category names:', categories.map(c => c.name).join(', '));
    }

    // Test filtering by category
    if (categories && categories.length > 0) {
      const testCategory = categories[0];
      console.log(`🔍 Testing category filter for "${testCategory.name}"...`);
      
      const categoryMappings = mappings?.filter(m => m.category_id === testCategory.id) || [];
      const brandIds = categoryMappings.map(m => m.brand_id);
      const categoryModels = allModels?.filter(m => brandIds.includes(m.brand_id)) || [];
      
      console.log(`  Found ${categoryModels.length} models in "${testCategory.name}" category`);
      if (categoryModels.length > 0) {
        console.log(`  Sample models: ${categoryModels.slice(0, 3).map(m => m.model_name).join(', ')}`);
      }
    }

    console.log('✅ Shoe database fix completed successfully!');

  } catch (error) {
    console.error('❌ Error fixing shoe database:', error);
    process.exit(1);
  }
}

// Run the script
fixShoeDatabase();
