/**
 * React Native Polyfills for FootFit App
 *
 * This file provides polyfills for missing JavaScript APIs in React Native
 * that are required by TensorFlow.js and other dependencies.
 */

import { log } from '@/utils/logger';

// Polyfill for structuredClone (not available in React Native)
if (typeof global !== 'undefined' && !global.structuredClone) {
  global.structuredClone = function<T>(obj: T): T {
    try {
      // Simple deep clone implementation for React Native
      if (obj === null || typeof obj !== 'object') {
        return obj;
      }
      
      if (obj instanceof Date) {
        return new Date(obj.getTime()) as T;
      }
      
      if (obj instanceof Array) {
        return obj.map(item => global.structuredClone(item)) as T;
      }
      
      if (typeof obj === 'object') {
        const cloned = {} as T;
        for (const key in obj) {
          if (obj.hasOwnProperty(key)) {
            (cloned as any)[key] = global.structuredClone((obj as any)[key]);
          }
        }
        return cloned;
      }
      
      return obj;
    } catch (error) {
      log.warn('structuredClone polyfill failed, falling back to JSON clone', 'Polyfills', error);
      // Fallback to JSON clone (less robust but works for most cases)
      return JSON.parse(JSON.stringify(obj));
    }
  };
  
  log.info('structuredClone polyfill installed', 'Polyfills');
}

// Polyfill for isTypedArray (required by TensorFlow.js)
if (typeof global !== 'undefined') {
  // Ensure util object exists
  if (typeof global.util === 'undefined') {
    global.util = {};
  }

  // Add isTypedArray function if missing
  if (!global.util.isTypedArray) {
    global.util.isTypedArray = function(value: any): boolean {
      return value instanceof Int8Array ||
             value instanceof Uint8Array ||
             value instanceof Uint8ClampedArray ||
             value instanceof Int16Array ||
             value instanceof Uint16Array ||
             value instanceof Int32Array ||
             value instanceof Uint32Array ||
             value instanceof Float32Array ||
             value instanceof Float64Array ||
             value instanceof BigInt64Array ||
             value instanceof BigUint64Array;
    };

    log.info('util.isTypedArray polyfill installed', 'Polyfills');
  }

  // Also try to add to common locations where TensorFlow.js might look
  if (typeof require !== 'undefined') {
    try {
      // Try to polyfill the util module that might be required by TensorFlow.js
      const Module = require('module');
      const originalRequire = Module.prototype.require;

      Module.prototype.require = function(id: string) {
        if (id === 'util') {
          return {
            isTypedArray: global.util.isTypedArray,
            ...originalRequire.call(this, id)
          };
        }
        return originalRequire.call(this, id);
      };

      log.info('util module require polyfill installed', 'Polyfills');
    } catch (error) {
      log.warn('util module require polyfill failed', 'Polyfills', error);
    }
  }
}

// Additional TensorFlow.js compatibility fixes
if (typeof global !== 'undefined') {
  // Ensure isTypedArray is available in multiple locations for TensorFlow.js
  const isTypedArrayFn = global.util.isTypedArray;

  // Add to global scope
  if (!global.isTypedArray) {
    global.isTypedArray = isTypedArrayFn;
  }

  // Add to process.binding if it exists (Node.js compatibility)
  if (global.process && typeof global.process.binding === 'function') {
    try {
      const originalBinding = global.process.binding;
      global.process.binding = function(name: string) {
        if (name === 'util') {
          return { isTypedArray: isTypedArrayFn };
        }
        return originalBinding.call(this, name);
      };
    } catch (error) {
      // Ignore binding errors
    }
  }
  // Ensure Buffer is available (required by some TensorFlow.js operations)
  if (typeof global.Buffer === 'undefined') {
    // Simple Buffer polyfill for React Native
    global.Buffer = {
      from: (data: any) => new Uint8Array(data),
      alloc: (size: number) => new Uint8Array(size),
      isBuffer: (obj: any) => obj instanceof Uint8Array,
    } as any;
    log.info('Buffer polyfill installed', 'Polyfills');
  }
  
  // Ensure process is available (required by some Node.js modules)
  if (typeof global.process === 'undefined') {
    global.process = {
      env: {},
      platform: 'react-native',
      version: '16.0.0',
      versions: { node: '16.0.0' },
      nextTick: (callback: Function) => setTimeout(callback, 0),
    } as any;
    log.info('process polyfill installed', 'Polyfills');
  }
  
  // Ensure crypto is available for TensorFlow.js random operations
  if (typeof global.crypto === 'undefined') {
    try {
      const { getRandomValues } = require('expo-crypto');
      global.crypto = {
        getRandomValues: getRandomValues,
        randomUUID: () => {
          // Simple UUID v4 implementation
          return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
          });
        }
      } as any;
      log.info('crypto polyfill installed', 'Polyfills');
    } catch (error) {
      // Fallback crypto implementation
      global.crypto = {
        getRandomValues: (array: any) => {
          for (let i = 0; i < array.length; i++) {
            array[i] = Math.floor(Math.random() * 256);
          }
          return array;
        },
        randomUUID: () => {
          return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
            const r = Math.random() * 16 | 0;
            const v = c === 'x' ? r : (r & 0x3 | 0x8);
            return v.toString(16);
          });
        }
      } as any;
      log.info('crypto fallback polyfill installed', 'Polyfills', error);
    }
  }
  
  // Ensure TextEncoder/TextDecoder are available
  if (typeof global.TextEncoder === 'undefined') {
    // Simple TextEncoder/TextDecoder polyfill
    global.TextEncoder = class {
      encode(input: string = '') {
        const bytes = new Uint8Array(input.length);
        for (let i = 0; i < input.length; i++) {
          bytes[i] = input.charCodeAt(i);
        }
        return bytes;
      }
    } as any;

    global.TextDecoder = class {
      decode(input: Uint8Array) {
        return String.fromCharCode(...Array.from(input));
      }
    } as any;

    log.info('TextEncoder/TextDecoder polyfill installed', 'Polyfills');
  }
}

// React Native specific fixes for TensorFlow.js
if (typeof global !== 'undefined') {
  // Fix for TensorFlow.js platform detection
  if (!global.navigator) {
    global.navigator = {
      userAgent: 'react-native',
      platform: 'react-native'
    } as any;
  }
  
  // Fix for missing performance API
  if (!global.performance) {
    global.performance = {
      now: () => Date.now(),
      mark: () => {},
      measure: () => {},
      getEntriesByName: () => [],
      getEntriesByType: () => [],
      clearMarks: () => {},
      clearMeasures: () => {}
    } as any;
  }
  
  // Fix for missing requestAnimationFrame
  if (!global.requestAnimationFrame) {
    global.requestAnimationFrame = (callback: FrameRequestCallback) => {
      return setTimeout(callback, 16); // ~60fps
    };
  }
  
  if (!global.cancelAnimationFrame) {
    global.cancelAnimationFrame = (id: number) => {
      clearTimeout(id);
    };
  }
}

// Supabase compatibility fixes
if (typeof global !== 'undefined') {
  // URL polyfill should be loaded automatically by react-native-url-polyfill/auto
  // We don't need to explicitly require it here to avoid bundling issues
  log.info('URL polyfill should be loaded automatically', 'Polyfills');
}

// Validate that critical polyfills are working
try {
  // Test structuredClone
  if (global.structuredClone) {
    const testObj = { a: 1, b: [2, 3] };
    const cloned = global.structuredClone(testObj);
    if (cloned.a !== 1 || cloned.b[0] !== 2) {
      throw new Error('structuredClone polyfill validation failed');
    }
  }

  // Test util.isTypedArray
  if (global.util && global.util.isTypedArray) {
    const testArray = new Float32Array([1, 2, 3]);
    const testRegularArray = [1, 2, 3];
    if (!global.util.isTypedArray(testArray) || global.util.isTypedArray(testRegularArray)) {
      throw new Error('util.isTypedArray polyfill validation failed');
    }
  }

  log.info('All polyfills initialized and validated successfully', 'Polyfills');
} catch (error) {
  log.error('Polyfill validation failed', 'Polyfills', error);
  throw error;
}

export { };

