-- FootFit Database Optimization SQL
-- Generated on: 2025-07-17T11:35:32.099Z
-- Purpose: Remove unused tables and columns for production-ready schema
-- 
-- IMPORTANT: Execute these commands in Supabase SQL Editor
-- Make sure to backup your database before running these commands
--

-- =====================================================
-- PHASE 1: REMOVE UNUSED TABLES
-- =====================================================

-- Remove recommendation_history table (unused in application)
DROP TABLE IF EXISTS recommendation_history;

-- Remove user_preferences table (unused in application)
DROP TABLE IF EXISTS user_preferences;

-- Remove shoe_history table (unused in application)
DROP TABLE IF EXISTS shoe_history;

-- Remove temp_shoe_data table (unused in application)
DROP TABLE IF EXISTS temp_shoe_data;

-- Remove migration_temp table (unused in application)
DROP TABLE IF EXISTS migration_temp;

-- Remove backup_shoe_models table (unused in application)
DROP TABLE IF EXISTS backup_shoe_models;

-- =====================================================
-- PHASE 2: REMOVE UNUSED COLUMNS FROM ESSENTIAL TABLES
-- =====================================================

-- Remove unused columns from shoe_models
ALTER TABLE shoe_models DROP COLUMN IF EXISTS model_code;
ALTER TABLE shoe_models DROP COLUMN IF EXISTS additional_images;
ALTER TABLE shoe_models DROP COLUMN IF EXISTS price_range_min;
ALTER TABLE shoe_models DROP COLUMN IF EXISTS price_range_max;
ALTER TABLE shoe_models DROP COLUMN IF EXISTS currency;
ALTER TABLE shoe_models DROP COLUMN IF EXISTS availability_status;
ALTER TABLE shoe_models DROP COLUMN IF EXISTS fit_type;
ALTER TABLE shoe_models DROP COLUMN IF EXISTS target_gender;
ALTER TABLE shoe_models DROP COLUMN IF EXISTS release_date;
ALTER TABLE shoe_models DROP COLUMN IF EXISTS popularity_score;
ALTER TABLE shoe_models DROP COLUMN IF EXISTS is_featured;
ALTER TABLE shoe_models DROP COLUMN IF EXISTS is_active;

-- =====================================================
-- VERIFICATION QUERIES
-- =====================================================

-- Check remaining tables
SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' ORDER BY table_name;

-- Verify essential tables have expected structure
SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'shoe_categories' ORDER BY ordinal_position;
SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'shoe_brands' ORDER BY ordinal_position;
SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'shoe_models' ORDER BY ordinal_position;
SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'brand_category_mapping' ORDER BY ordinal_position;
SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'shoe_sizes' ORDER BY ordinal_position;
SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'profiles' ORDER BY ordinal_position;
SELECT column_name, data_type FROM information_schema.columns WHERE table_name = 'measurements' ORDER BY ordinal_position;