-- FootFit Database Cleanup SQL
-- Generated on: 2025-07-17T11:15:01.618Z
-- Purpose: Remove unused tables from FootFit database
-- 
-- IMPORTANT: Execute these commands in Supabase SQL Editor
-- Make sure to backup your database before running these commands
--

DROP TABLE IF EXISTS user_shoe_preferences;
DROP TABLE IF EXISTS recommendation_history;
DROP TABLE IF EXISTS user_preferences;
DROP TABLE IF EXISTS shoe_history;
DROP TABLE IF EXISTS temp_shoe_data;
DROP TABLE IF EXISTS migration_temp;
DROP TABLE IF EXISTS backup_shoe_models;

-- Verification query to check remaining tables
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;
