-- FootFit Comprehensive Shoe Database Population Script
-- This script creates a complete shoe catalog with 150+ models across 29 brands
-- Run this script in Supabase SQL Editor with admin privileges
-- 
-- IMPORTANT: This script temporarily disables RLS for data population
-- Only run this in a development/academic environment

-- =====================================================
-- PHASE 1: PREPARE DATABASE FOR POPULATION
-- =====================================================

-- Temporarily disable RLS for data insertion (admin only)
ALTER TABLE shoe_categories DISABLE ROW LEVEL SECURITY;
ALTER TABLE shoe_brands DISABLE ROW LEVEL SECURITY;
ALTER TABLE shoe_models DISABLE ROW LEVEL SECURITY;
ALTER TABLE brand_category_mapping DISABLE ROW LEVEL SECURITY;
ALTER TABLE shoe_sizes DISABLE ROW LEVEL SECURITY;

-- Clear existing data for fresh population
DELETE FROM shoe_sizes;
DELETE FROM brand_category_mapping;
DELETE FROM shoe_models;
DELETE FROM shoe_categories;
DELETE FROM shoe_brands;

-- =====================================================
-- PHASE 2: INSERT CORE DATA STRUCTURE
-- =====================================================

-- Insert shoe categories (5 categories as specified)
INSERT INTO shoe_categories (id, name, description, display_order, icon_name, color_theme, is_active, created_at) VALUES
('cat_performance_sports', 'Performance Sports', 'High-performance athletic shoes for running, training, and sports', 1, 'sports', '#FF6B35', true, NOW()),
('cat_outdoor_hiking', 'Outdoor & Hiking', 'Durable shoes for outdoor activities, hiking, and trail running', 2, 'mountain', '#4CAF50', true, NOW()),
('cat_everyday_casual', 'Everyday Casual', 'Comfortable casual shoes for daily wear and lifestyle', 3, 'casual', '#2196F3', true, NOW()),
('cat_dress_formal', 'Dress & Formal', 'Formal and dress shoes for professional and special occasions', 4, 'formal', '#9C27B0', true, NOW()),
('cat_specialty_comfort', 'Specialty Comfort', 'Specialized comfort shoes with advanced cushioning and support', 5, 'comfort', '#FF9800', true, NOW());

-- Insert shoe brands (29 brands total)
INSERT INTO shoe_brands (id, name, description, country_origin, website_url, logo_url, is_premium, is_active, created_at) VALUES
-- Tier 1 Brands (8+ models each)
('brand_nike', 'Nike', 'Just Do It - Leading athletic footwear and apparel brand', 'USA', 'https://www.nike.com', 'https://logos-world.net/wp-content/uploads/2020/04/Nike-Logo.png', true, true, NOW()),
('brand_adidas', 'Adidas', 'Impossible is Nothing - German multinational corporation', 'Germany', 'https://www.adidas.com', 'https://logos-world.net/wp-content/uploads/2020/04/Adidas-Logo.png', true, true, NOW()),
('brand_new_balance', 'New Balance', 'Endorsed by No One - Premium athletic footwear', 'USA', 'https://www.newbalance.com', 'https://logos-world.net/wp-content/uploads/2020/04/New-Balance-Logo.png', true, true, NOW()),
('brand_asics', 'ASICS', 'Anima Sana In Corpore Sano - Japanese athletic equipment company', 'Japan', 'https://www.asics.com', 'https://logos-world.net/wp-content/uploads/2020/04/ASICS-Logo.png', true, true, NOW()),
('brand_brooks', 'Brooks', 'Run Happy - Specialized running shoe company', 'USA', 'https://www.brooksrunning.com', 'https://logos-world.net/wp-content/uploads/2020/04/Brooks-Logo.png', true, true, NOW()),
('brand_puma', 'Puma', 'Forever Faster - German multinational corporation', 'Germany', 'https://www.puma.com', 'https://logos-world.net/wp-content/uploads/2020/04/Puma-Logo.png', true, true, NOW()),

-- Tier 2 Brands (5-7 models each)
('brand_hoka', 'Hoka', 'Fly Human Fly - Maximalist running shoes', 'France', 'https://www.hoka.com', 'https://logos-world.net/wp-content/uploads/2020/04/Hoka-Logo.png', true, true, NOW()),
('brand_vans', 'Vans', 'Off The Wall - Skateboarding shoes and lifestyle brand', 'USA', 'https://www.vans.com', 'https://logos-world.net/wp-content/uploads/2020/04/Vans-Logo.png', false, true, NOW()),
('brand_converse', 'Converse', 'All Star - Classic American shoe company', 'USA', 'https://www.converse.com', 'https://logos-world.net/wp-content/uploads/2020/04/Converse-Logo.png', false, true, NOW()),
('brand_merrell', 'Merrell', 'Nothing Feels Better - Outdoor footwear company', 'USA', 'https://www.merrell.com', 'https://logos-world.net/wp-content/uploads/2020/04/Merrell-Logo.png', true, true, NOW()),
('brand_salomon', 'Salomon', 'Time to Play - French sports equipment company', 'France', 'https://www.salomon.com', 'https://logos-world.net/wp-content/uploads/2020/04/Salomon-Logo.png', true, true, NOW()),
('brand_skechers', 'Skechers', 'The Comfort Technology Company', 'USA', 'https://www.skechers.com', 'https://logos-world.net/wp-content/uploads/2020/04/Skechers-Logo.png', false, true, NOW()),

-- Tier 3 Brands (3-4 models each)
('brand_under_armour', 'Under Armour', 'I Will - American sports equipment company', 'USA', 'https://www.underarmour.com', 'https://logos-world.net/wp-content/uploads/2020/04/Under-Armour-Logo.png', true, true, NOW()),
('brand_jordan', 'Jordan Brand', 'Jumpman - Nike subsidiary for basketball shoes', 'USA', 'https://www.nike.com/jordan', 'https://logos-world.net/wp-content/uploads/2020/04/Jordan-Logo.png', true, true, NOW()),
('brand_reebok', 'Reebok', 'Be More Human - Athletic footwear and apparel', 'UK', 'https://www.reebok.com', 'https://logos-world.net/wp-content/uploads/2020/04/Reebok-Logo.png', true, true, NOW()),
('brand_the_north_face', 'The North Face', 'Never Stop Exploring - Outdoor recreation products', 'USA', 'https://www.thenorthface.com', 'https://logos-world.net/wp-content/uploads/2020/04/North-Face-Logo.png', true, true, NOW()),
('brand_keen', 'KEEN', 'Hybrid Life - Outdoor and casual footwear', 'USA', 'https://www.keenfootwear.com', 'https://logos-world.net/wp-content/uploads/2020/04/Keen-Logo.png', true, true, NOW()),
('brand_columbia', 'Columbia', 'Tested Tough - Outdoor apparel and footwear', 'USA', 'https://www.columbia.com', 'https://logos-world.net/wp-content/uploads/2020/04/Columbia-Logo.png', false, true, NOW()),
('brand_allbirds', 'Allbirds', 'Comfort Meets Conscious - Sustainable footwear', 'USA', 'https://www.allbirds.com', 'https://logos-world.net/wp-content/uploads/2020/04/Allbirds-Logo.png', true, true, NOW()),
('brand_birkenstock', 'Birkenstock', 'Original Footbed - German sandal manufacturer', 'Germany', 'https://www.birkenstock.com', 'https://logos-world.net/wp-content/uploads/2020/04/Birkenstock-Logo.png', true, true, NOW()),
('brand_dansko', 'Dansko', 'Walk in Comfort - Professional and casual clogs', 'Denmark', 'https://www.dansko.com', 'https://logos-world.net/wp-content/uploads/2020/04/Dansko-Logo.png', true, true, NOW()),
('brand_oofos', 'OOFOS', 'Feel the OO - Recovery footwear', 'USA', 'https://www.oofos.com', 'https://logos-world.net/wp-content/uploads/2020/04/Oofos-Logo.png', true, true, NOW()),
('brand_cole_haan', 'Cole Haan', 'Grand Innovation - Luxury footwear and accessories', 'USA', 'https://www.colehaan.com', 'https://logos-world.net/wp-content/uploads/2020/04/Cole-Haan-Logo.png', true, true, NOW()),
('brand_clarks', 'Clarks', 'Life. Styled. - British footwear manufacturer', 'UK', 'https://www.clarks.com', 'https://logos-world.net/wp-content/uploads/2020/04/Clarks-Logo.png', true, true, NOW()),
('brand_johnston_murphy', 'Johnston & Murphy', 'Crafted in America - Premium dress shoes', 'USA', 'https://www.johnstonmurphy.com', 'https://logos-world.net/wp-content/uploads/2020/04/Johnston-Murphy-Logo.png', true, true, NOW()),
('brand_gucci', 'Gucci', 'Quality is Remembered - Italian luxury fashion house', 'Italy', 'https://www.gucci.com', 'https://logos-world.net/wp-content/uploads/2020/04/Gucci-Logo.png', true, true, NOW()),
('brand_louis_vuitton', 'Louis Vuitton', 'L.V - French luxury fashion house', 'France', 'https://www.louisvuitton.com', 'https://logos-world.net/wp-content/uploads/2020/04/Louis-Vuitton-Logo.png', true, true, NOW()),
('brand_balenciaga', 'Balenciaga', 'Luxury Fashion - Spanish luxury fashion house', 'Spain', 'https://www.balenciaga.com', 'https://logos-world.net/wp-content/uploads/2020/04/Balenciaga-Logo.png', true, true, NOW()),
('brand_timberland', 'Timberland', 'Nature Needs Heroes - Outdoor footwear and apparel', 'USA', 'https://www.timberland.com', 'https://logos-world.net/wp-content/uploads/2020/04/Timberland-Logo.png', true, true, NOW()),
('brand_dr_martens', 'Dr. Martens', 'With Bouncing Soles - British footwear and clothing brand', 'UK', 'https://www.drmartens.com', 'https://logos-world.net/wp-content/uploads/2020/04/Dr-Martens-Logo.png', true, true, NOW());

-- =====================================================
-- PHASE 3: BRAND-CATEGORY MAPPINGS
-- =====================================================

-- Insert brand-category mappings for comprehensive coverage
INSERT INTO brand_category_mapping (brand_id, category_id, created_at) VALUES
-- Performance Sports mappings
('brand_nike', 'cat_performance_sports', NOW()),
('brand_adidas', 'cat_performance_sports', NOW()),
('brand_new_balance', 'cat_performance_sports', NOW()),
('brand_asics', 'cat_performance_sports', NOW()),
('brand_brooks', 'cat_performance_sports', NOW()),
('brand_hoka', 'cat_performance_sports', NOW()),
('brand_puma', 'cat_performance_sports', NOW()),
('brand_under_armour', 'cat_performance_sports', NOW()),
('brand_jordan', 'cat_performance_sports', NOW()),
('brand_reebok', 'cat_performance_sports', NOW()),

-- Outdoor & Hiking mappings
('brand_merrell', 'cat_outdoor_hiking', NOW()),
('brand_salomon', 'cat_outdoor_hiking', NOW()),
('brand_the_north_face', 'cat_outdoor_hiking', NOW()),
('brand_keen', 'cat_outdoor_hiking', NOW()),
('brand_columbia', 'cat_outdoor_hiking', NOW()),
('brand_timberland', 'cat_outdoor_hiking', NOW()),
('brand_nike', 'cat_outdoor_hiking', NOW()),
('brand_adidas', 'cat_outdoor_hiking', NOW()),
('brand_new_balance', 'cat_outdoor_hiking', NOW()),

-- Everyday Casual mappings
('brand_vans', 'cat_everyday_casual', NOW()),
('brand_converse', 'cat_everyday_casual', NOW()),
('brand_puma', 'cat_everyday_casual', NOW()),
('brand_skechers', 'cat_everyday_casual', NOW()),
('brand_nike', 'cat_everyday_casual', NOW()),
('brand_adidas', 'cat_everyday_casual', NOW()),
('brand_new_balance', 'cat_everyday_casual', NOW()),
('brand_allbirds', 'cat_everyday_casual', NOW()),
('brand_dr_martens', 'cat_everyday_casual', NOW()),

-- Dress & Formal mappings
('brand_cole_haan', 'cat_dress_formal', NOW()),
('brand_clarks', 'cat_dress_formal', NOW()),
('brand_johnston_murphy', 'cat_dress_formal', NOW()),
('brand_gucci', 'cat_dress_formal', NOW()),
('brand_louis_vuitton', 'cat_dress_formal', NOW()),
('brand_balenciaga', 'cat_dress_formal', NOW()),
('brand_dr_martens', 'cat_dress_formal', NOW()),

-- Specialty Comfort mappings
('brand_allbirds', 'cat_specialty_comfort', NOW()),
('brand_birkenstock', 'cat_specialty_comfort', NOW()),
('brand_dansko', 'cat_specialty_comfort', NOW()),
('brand_oofos', 'cat_specialty_comfort', NOW()),
('brand_hoka', 'cat_specialty_comfort', NOW()),
('brand_brooks', 'cat_specialty_comfort', NOW()),
('brand_skechers', 'cat_specialty_comfort', NOW()),
('brand_new_balance', 'cat_specialty_comfort', NOW());

-- =====================================================
-- PHASE 4: COMPREHENSIVE SHOE MODELS (150+ models)
-- =====================================================

-- TIER 1 BRANDS: Nike Models (10 models)
INSERT INTO shoe_models (id, brand_id, category_id, model_name, model_code, description, image_url, price_range_min, price_range_max, currency, availability_status, fit_type, target_gender, popularity_score, is_featured, is_active, created_at) VALUES
('model_nike_pegasus40', 'brand_nike', 'cat_performance_sports', 'Air Zoom Pegasus 40', 'DV3853', 'Responsive cushioning in the Pegasus 40 gives you a bouncy ride for everyday runs', 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/99486859-0ff7-4e79-b9e2-dfa4ee40699d/air-zoom-pegasus-40-mens-road-running-shoes-6C7ZhF.png', 130.00, 130.00, 'USD', 'available', 'regular', 'unisex', 95, true, true, NOW()),
('model_nike_react_infinity', 'brand_nike', 'cat_performance_sports', 'React Infinity Run Flyknit 3', 'CT2357', 'Designed to help reduce injury and keep you on the run', 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/b7d9211c-26e7-431a-ac24-b0540fb3c00f/react-infinity-run-flyknit-3-mens-road-running-shoes-6Q1tFM.png', 160.00, 160.00, 'USD', 'available', 'regular', 'unisex', 88, false, true, NOW()),
('model_nike_air_max_270', 'brand_nike', 'cat_everyday_casual', 'Air Max 270', 'AH8050', 'Inspired by two icons of big Air: the Air Max 180 and Air Max 93', 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/awjogtdnqxniqqk0wpgf/air-max-270-mens-shoes-KkLcGR.png', 150.00, 150.00, 'USD', 'available', 'regular', 'unisex', 92, true, true, NOW()),
('model_nike_air_force_1', 'brand_nike', 'cat_everyday_casual', 'Air Force 1 07', 'CW2288', 'The radiance lives on in the Nike Air Force 1 07', 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/b7d9211c-26e7-431a-ac24-b0540fb3c00f/air-force-1-07-mens-shoes-jBrhbr.png', 90.00, 110.00, 'USD', 'available', 'regular', 'unisex', 98, true, true, NOW()),
('model_nike_blazer_mid', 'brand_nike', 'cat_everyday_casual', 'Blazer Mid 77 Vintage', 'BQ6806', 'Styled for the 70s. Loved in the 80s. Classic in the 90s. Ready for the future.', 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/b7d9211c-26e7-431a-ac24-b0540fb3c00f/blazer-mid-77-vintage-mens-shoes-nw30B2.png', 100.00, 100.00, 'USD', 'available', 'regular', 'unisex', 85, false, true, NOW()),
('model_nike_acg_mountain_fly', 'brand_nike', 'cat_outdoor_hiking', 'ACG Mountain Fly Low', 'DA9157', 'Designed for the outdoors with durable materials and aggressive traction', 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/b7d9211c-26e7-431a-ac24-b0540fb3c00f/acg-mountain-fly-low-mens-shoes-6Q1tFM.png', 120.00, 120.00, 'USD', 'available', 'regular', 'unisex', 78, false, true, NOW()),
('model_nike_wildhorse_8', 'brand_nike', 'cat_outdoor_hiking', 'Air Zoom Terra Kiger 8', 'CW6062', 'Conquer the trails with responsive cushioning and rugged traction', 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/b7d9211c-26e7-431a-ac24-b0540fb3c00f/air-zoom-terra-kiger-8-mens-trail-running-shoes-6Q1tFM.png', 130.00, 130.00, 'USD', 'available', 'regular', 'unisex', 82, false, true, NOW()),
('model_nike_metcon_8', 'brand_nike', 'cat_performance_sports', 'Metcon 8', 'DX9991', 'The gold standard for weight training—even tougher and more stable', 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/b7d9211c-26e7-431a-ac24-b0540fb3c00f/metcon-8-mens-training-shoes-6Q1tFM.png', 130.00, 130.00, 'USD', 'available', 'regular', 'unisex', 87, false, true, NOW()),
('model_nike_zoom_fly_5', 'brand_nike', 'cat_performance_sports', 'Zoom Fly 5', 'DM8968', 'Run long, run fast, run far in the Zoom Fly 5', 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/b7d9211c-26e7-431a-ac24-b0540fb3c00f/zoom-fly-5-mens-road-running-shoes-6Q1tFM.png', 160.00, 160.00, 'USD', 'available', 'regular', 'unisex', 89, false, true, NOW()),
('model_nike_dunk_low', 'brand_nike', 'cat_everyday_casual', 'Dunk Low', 'DD1391', 'Created for the hardwood but taken to the streets', 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/b7d9211c-26e7-431a-ac24-b0540fb3c00f/dunk-low-mens-shoes-6Q1tFM.png', 100.00, 110.00, 'USD', 'available', 'regular', 'unisex', 94, true, true, NOW()),

-- TIER 1 BRANDS: Adidas Models (10 models)
('model_adidas_ultraboost_23', 'brand_adidas', 'cat_performance_sports', 'Ultraboost 23', 'ID8011', 'Feel the energy return with every step in these running shoes', 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/b7d9211c26e7431aac24b0540fb3c00f/ultraboost-23-shoes.jpg', 190.00, 190.00, 'USD', 'available', 'regular', 'unisex', 93, true, true, NOW()),
('model_adidas_nmd_r1', 'brand_adidas', 'cat_everyday_casual', 'NMD_R1', 'S79162', 'Modern nomad meets street-ready style', 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/b7d9211c26e7431aac24b0540fb3c00f/nmd-r1-shoes.jpg', 130.00, 130.00, 'USD', 'available', 'regular', 'unisex', 91, true, true, NOW()),
('model_adidas_stan_smith', 'brand_adidas', 'cat_everyday_casual', 'Stan Smith', 'FX5500', 'Clean and classic with an eco-friendly build', 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/b7d9211c26e7431aac24b0540fb3c00f/stan-smith-shoes.jpg', 80.00, 100.00, 'USD', 'available', 'regular', 'unisex', 96, true, true, NOW()),
('model_adidas_gazelle', 'brand_adidas', 'cat_everyday_casual', 'Gazelle', 'BB5476', 'Vintage-inspired shoes with modern comfort', 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/b7d9211c26e7431aac24b0540fb3c00f/gazelle-shoes.jpg', 80.00, 90.00, 'USD', 'available', 'regular', 'unisex', 88, false, true, NOW()),
('model_adidas_supernova_3', 'brand_adidas', 'cat_performance_sports', 'Supernova 3', 'IG6309', 'Comfortable running shoes for daily training', 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/b7d9211c26e7431aac24b0540fb3c00f/supernova-3-running-shoes.jpg', 100.00, 100.00, 'USD', 'available', 'regular', 'unisex', 84, false, true, NOW()),
('model_adidas_terrex_swift', 'brand_adidas', 'cat_outdoor_hiking', 'Terrex Swift R3 GTX', 'FZ3267', 'Waterproof hiking shoes built for speed and agility', 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/b7d9211c26e7431aac24b0540fb3c00f/terrex-swift-r3-gtx-hiking-shoes.jpg', 140.00, 140.00, 'USD', 'available', 'regular', 'unisex', 86, false, true, NOW()),
('model_adidas_terrex_free_hiker', 'brand_adidas', 'cat_outdoor_hiking', 'Terrex Free Hiker 2.0', 'FZ2518', 'Lightweight hiking shoes with Boost cushioning', 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/b7d9211c26e7431aac24b0540fb3c00f/terrex-free-hiker-2-hiking-shoes.jpg', 200.00, 200.00, 'USD', 'available', 'regular', 'unisex', 89, false, true, NOW()),
('model_adidas_samba_og', 'brand_adidas', 'cat_everyday_casual', 'Samba OG', 'B75807', 'Classic soccer-inspired shoes with vintage appeal', 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/b7d9211c26e7431aac24b0540fb3c00f/samba-og-shoes.jpg', 80.00, 90.00, 'USD', 'available', 'regular', 'unisex', 92, true, true, NOW()),
('model_adidas_4dfwd_3', 'brand_adidas', 'cat_performance_sports', '4DFWD 3', 'HP7540', 'Revolutionary 3D-printed midsole for forward motion', 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/b7d9211c26e7431aac24b0540fb3c00f/4dfwd-3-running-shoes.jpg', 200.00, 200.00, 'USD', 'available', 'regular', 'unisex', 87, false, true, NOW()),
('model_adidas_campus_00s', 'brand_adidas', 'cat_everyday_casual', 'Campus 00s', 'HQ8708', 'Y2K-inspired shoes with a chunky silhouette', 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/b7d9211c26e7431aac24b0540fb3c00f/campus-00s-shoes.jpg', 90.00, 90.00, 'USD', 'available', 'regular', 'unisex', 85, false, true, NOW()),

-- TIER 1 BRANDS: New Balance Models (10 models)
('model_nb_fresh_foam_1080v12', 'brand_new_balance', 'cat_performance_sports', 'Fresh Foam X 1080v12', 'M1080V12', 'The most cushioned shoe in our running lineup', 'https://nb.scene7.com/is/image/NB/m1080v12_nb_02_i?$pdpflexf2$&wid=440&hei=440', 150.00, 150.00, 'USD', 'available', 'regular', 'unisex', 91, true, true, NOW()),
('model_nb_990v5', 'brand_new_balance', 'cat_everyday_casual', '990v5', 'M990V5', 'Made in USA premium lifestyle sneaker', 'https://nb.scene7.com/is/image/NB/m990v5_nb_02_i?$pdpflexf2$&wid=440&hei=440', 185.00, 185.00, 'USD', 'available', 'regular', 'unisex', 95, true, true, NOW()),
('model_nb_574_core', 'brand_new_balance', 'cat_everyday_casual', '574 Core', 'ML574EGG', 'The most New Balance shoe ever', 'https://nb.scene7.com/is/image/NB/ml574egg_nb_02_i?$pdpflexf2$&wid=440&hei=440', 80.00, 80.00, 'USD', 'available', 'regular', 'unisex', 88, false, true, NOW()),
('model_nb_fuelcell_rebel_v3', 'brand_new_balance', 'cat_performance_sports', 'FuelCell Rebel v3', 'MFCXLG3', 'Lightweight racing shoe with propulsive feel', 'https://nb.scene7.com/is/image/NB/mfcxlg3_nb_02_i?$pdpflexf2$&wid=440&hei=440', 130.00, 130.00, 'USD', 'available', 'regular', 'unisex', 87, false, true, NOW()),
('model_nb_hierro_v7', 'brand_new_balance', 'cat_outdoor_hiking', 'Fresh Foam X Hierro v7', 'MTHIERV7', 'Trail running shoe with Vibram Megagrip outsole', 'https://nb.scene7.com/is/image/NB/mthierv7_nb_02_i?$pdpflexf2$&wid=440&hei=440', 135.00, 135.00, 'USD', 'available', 'regular', 'unisex', 84, false, true, NOW()),
('model_nb_more_v4', 'brand_new_balance', 'cat_specialty_comfort', 'Fresh Foam X More v4', 'MMORV4', 'Ultra-plush cushioning for maximum comfort', 'https://nb.scene7.com/is/image/NB/mmorv4_nb_02_i?$pdpflexf2$&wid=440&hei=440', 140.00, 140.00, 'USD', 'available', 'regular', 'unisex', 89, false, true, NOW()),
('model_nb_327', 'brand_new_balance', 'cat_everyday_casual', '327', 'MS327LAA', 'Retro-inspired lifestyle sneaker', 'https://nb.scene7.com/is/image/NB/ms327laa_nb_02_i?$pdpflexf2$&wid=440&hei=440', 90.00, 90.00, 'USD', 'available', 'regular', 'unisex', 86, false, true, NOW()),
('model_nb_vongo_v5', 'brand_new_balance', 'cat_performance_sports', 'Fresh Foam X Vongo v5', 'MVNGOV5', 'Stability running shoe with medial post', 'https://nb.scene7.com/is/image/NB/mvngov5_nb_02_i?$pdpflexf2$&wid=440&hei=440', 140.00, 140.00, 'USD', 'available', 'regular', 'unisex', 82, false, true, NOW()),
('model_nb_minimus_trail', 'brand_new_balance', 'cat_outdoor_hiking', 'Minimus Trail v20', 'MT20V1', 'Minimalist trail running shoe', 'https://nb.scene7.com/is/image/NB/mt20v1_nb_02_i?$pdpflexf2$&wid=440&hei=440', 100.00, 100.00, 'USD', 'available', 'regular', 'unisex', 78, false, true, NOW()),
('model_nb_2002r', 'brand_new_balance', 'cat_everyday_casual', '2002R', 'ML2002RA', 'Y2K-inspired lifestyle sneaker with ABZORB cushioning', 'https://nb.scene7.com/is/image/NB/ml2002ra_nb_02_i?$pdpflexf2$&wid=440&hei=440', 100.00, 100.00, 'USD', 'available', 'regular', 'unisex', 90, true, true, NOW()),

-- TIER 1 BRANDS: ASICS Models (9 models)
('model_asics_gel_nimbus_25', 'brand_asics', 'cat_performance_sports', 'GEL-Nimbus 25', '1011B547', 'Maximum cushioning for long-distance comfort', 'https://images.asics.com/is/image/asics/1011B547_001_SR_RT_GLB?$zoom$', 160.00, 160.00, 'USD', 'available', 'regular', 'unisex', 92, true, true, NOW()),
('model_asics_gel_kayano_30', 'brand_asics', 'cat_performance_sports', 'GEL-Kayano 30', '1011B492', 'Stability running shoe with adaptive guidance system', 'https://images.asics.com/is/image/asics/1011B492_001_SR_RT_GLB?$zoom$', 160.00, 160.00, 'USD', 'available', 'regular', 'unisex', 89, false, true, NOW()),
('model_asics_gel_cumulus_25', 'brand_asics', 'cat_performance_sports', 'GEL-Cumulus 25', '1011B394', 'Everyday neutral running shoe with GEL technology', 'https://images.asics.com/is/image/asics/1011B394_001_SR_RT_GLB?$zoom$', 130.00, 130.00, 'USD', 'available', 'regular', 'unisex', 85, false, true, NOW()),
('model_asics_gel_venture_9', 'brand_asics', 'cat_outdoor_hiking', 'GEL-Venture 9', '1011B487', 'Trail running shoe with rugged outsole', 'https://images.asics.com/is/image/asics/1011B487_001_SR_RT_GLB?$zoom$', 70.00, 70.00, 'USD', 'available', 'regular', 'unisex', 80, false, true, NOW()),
('model_asics_gel_trabuco_11', 'brand_asics', 'cat_outdoor_hiking', 'GEL-Trabuco 11', '1011B566', 'Technical trail running shoe for challenging terrain', 'https://images.asics.com/is/image/asics/1011B566_001_SR_RT_GLB?$zoom$', 140.00, 140.00, 'USD', 'available', 'regular', 'unisex', 83, false, true, NOW()),
('model_asics_gel_quantum_360', 'brand_asics', 'cat_performance_sports', 'GEL-Quantum 360 VII', '1201A365', 'Full-length GEL technology for ultimate cushioning', 'https://images.asics.com/is/image/asics/1201A365_001_SR_RT_GLB?$zoom$', 180.00, 180.00, 'USD', 'available', 'regular', 'unisex', 87, false, true, NOW()),
('model_asics_gel_lyte_iii', 'brand_asics', 'cat_everyday_casual', 'GEL-Lyte III OG', '1191A266', 'Retro lifestyle sneaker with split tongue design', 'https://images.asics.com/is/image/asics/1191A266_001_SR_RT_GLB?$zoom$', 90.00, 90.00, 'USD', 'available', 'regular', 'unisex', 88, false, true, NOW()),
('model_asics_novablast_3', 'brand_asics', 'cat_performance_sports', 'NOVABLAST 3', '1011B458', 'Energetic running shoe with trampoline-inspired outsole', 'https://images.asics.com/is/image/asics/1011B458_001_SR_RT_GLB?$zoom$', 130.00, 130.00, 'USD', 'available', 'regular', 'unisex', 86, false, true, NOW()),
('model_asics_metarun', 'brand_asics', 'cat_performance_sports', 'MetaRun', '1011A603', 'Premium running shoe with adaptive guidance system', 'https://images.asics.com/is/image/asics/1011A603_001_SR_RT_GLB?$zoom$', 250.00, 250.00, 'USD', 'available', 'regular', 'unisex', 84, false, true, NOW()),

-- TIER 1 BRANDS: Brooks Models (9 models)
('model_brooks_ghost_15', 'brand_brooks', 'cat_performance_sports', 'Ghost 15', '110393', 'Smooth and soft ride for everyday runs', 'https://www.brooksrunning.com/dw/image/v2/BGQZ_PRD/on/demandware.static/-/Sites-brooks-master-catalog/default/dw8c8b8b8b/images/110393/110393_092_l.jpg', 140.00, 140.00, 'USD', 'available', 'regular', 'unisex', 93, true, true, NOW()),
('model_brooks_glycerin_20', 'brand_brooks', 'cat_specialty_comfort', 'Glycerin 20', '110356', 'Supreme softness with DNA LOFT v3 cushioning', 'https://www.brooksrunning.com/dw/image/v2/BGQZ_PRD/on/demandware.static/-/Sites-brooks-master-catalog/default/dw8c8b8b8b/images/110356/110356_092_l.jpg', 160.00, 160.00, 'USD', 'available', 'regular', 'unisex', 91, true, true, NOW()),
('model_brooks_adrenaline_gts_23', 'brand_brooks', 'cat_performance_sports', 'Adrenaline GTS 23', '110381', 'Trusted stability shoe with GuideRails support', 'https://www.brooksrunning.com/dw/image/v2/BGQZ_PRD/on/demandware.static/-/Sites-brooks-master-catalog/default/dw8c8b8b8b/images/110381/110381_092_l.jpg', 140.00, 140.00, 'USD', 'available', 'regular', 'unisex', 89, false, true, NOW()),
('model_brooks_cascadia_16', 'brand_brooks', 'cat_outdoor_hiking', 'Cascadia 16', '110343', 'Trail running shoe with Pivot Post system', 'https://www.brooksrunning.com/dw/image/v2/BGQZ_PRD/on/demandware.static/-/Sites-brooks-master-catalog/default/dw8c8b8b8b/images/110343/110343_092_l.jpg', 140.00, 140.00, 'USD', 'available', 'regular', 'unisex', 85, false, true, NOW()),
('model_brooks_hyperion_tempo', 'brand_brooks', 'cat_performance_sports', 'Hyperion Tempo', '110339', 'Lightweight training shoe for speed work', 'https://www.brooksrunning.com/dw/image/v2/BGQZ_PRD/on/demandware.static/-/Sites-brooks-master-catalog/default/dw8c8b8b8b/images/110339/110339_092_l.jpg', 150.00, 150.00, 'USD', 'available', 'regular', 'unisex', 87, false, true, NOW()),
('model_brooks_caldera_6', 'brand_brooks', 'cat_outdoor_hiking', 'Caldera 6', '110369', 'Cushioned trail running shoe for long distances', 'https://www.brooksrunning.com/dw/image/v2/BGQZ_PRD/on/demandware.static/-/Sites-brooks-master-catalog/default/dw8c8b8b8b/images/110369/110369_092_l.jpg', 130.00, 130.00, 'USD', 'available', 'regular', 'unisex', 82, false, true, NOW()),
('model_brooks_launch_9', 'brand_brooks', 'cat_performance_sports', 'Launch 9', '110350', 'Lightweight and responsive for fast runs', 'https://www.brooksrunning.com/dw/image/v2/BGQZ_PRD/on/demandware.static/-/Sites-brooks-master-catalog/default/dw8c8b8b8b/images/110350/110350_092_l.jpg', 100.00, 100.00, 'USD', 'available', 'regular', 'unisex', 84, false, true, NOW()),
('model_brooks_addiction_15', 'brand_brooks', 'cat_specialty_comfort', 'Addiction 15', '110314', 'Maximum support for severe overpronation', 'https://www.brooksrunning.com/dw/image/v2/BGQZ_PRD/on/demandware.static/-/Sites-brooks-master-catalog/default/dw8c8b8b8b/images/110314/110314_092_l.jpg', 140.00, 140.00, 'USD', 'available', 'regular', 'unisex', 86, false, true, NOW()),
('model_brooks_levitate_5', 'brand_brooks', 'cat_performance_sports', 'Levitate 5', '110345', 'Energized cushioning with DNA AMP midsole', 'https://www.brooksrunning.com/dw/image/v2/BGQZ_PRD/on/demandware.static/-/Sites-brooks-master-catalog/default/dw8c8b8b8b/images/110345/110345_092_l.jpg', 150.00, 150.00, 'USD', 'available', 'regular', 'unisex', 83, false, true, NOW()),

-- TIER 1 BRANDS: Puma Models (9 models)
('model_puma_velocity_nitro_3', 'brand_puma', 'cat_performance_sports', 'Velocity Nitro 3', '377218', 'Lightweight running shoe with NITRO foam', 'https://images.puma.com/image/upload/f_auto,q_auto,b_rgb:fafafa,w_2000,h_2000/global/377218/01/sv01/fnd/PNA/fmt/png/Velocity-NITRO-3-Mens-Running-Shoes', 110.00, 110.00, 'USD', 'available', 'regular', 'unisex', 88, false, true, NOW()),
('model_puma_suede_classic', 'brand_puma', 'cat_everyday_casual', 'Suede Classic XXI', '374915', 'Iconic lifestyle sneaker with suede upper', 'https://images.puma.com/image/upload/f_auto,q_auto,b_rgb:fafafa,w_2000,h_2000/global/374915/01/sv01/fnd/PNA/fmt/png/Suede-Classic-XXI-Sneakers', 70.00, 70.00, 'USD', 'available', 'regular', 'unisex', 92, true, true, NOW()),
('model_puma_rs_x', 'brand_puma', 'cat_everyday_casual', 'RS-X Efekt', '380462', 'Chunky retro sneaker with bold design', 'https://images.puma.com/image/upload/f_auto,q_auto,b_rgb:fafafa,w_2000,h_2000/global/380462/01/sv01/fnd/PNA/fmt/png/RS-X-Efekt-Sneakers', 90.00, 90.00, 'USD', 'available', 'regular', 'unisex', 85, false, true, NOW()),
('model_puma_deviate_nitro_2', 'brand_puma', 'cat_performance_sports', 'Deviate Nitro 2', '376587', 'Carbon plate running shoe for racing', 'https://images.puma.com/image/upload/f_auto,q_auto,b_rgb:fafafa,w_2000,h_2000/global/376587/01/sv01/fnd/PNA/fmt/png/Deviate-NITRO-2-Mens-Running-Shoes', 200.00, 200.00, 'USD', 'available', 'regular', 'unisex', 86, false, true, NOW()),
('model_puma_cali_court', 'brand_puma', 'cat_everyday_casual', 'Cali Court', '371116', 'California-inspired lifestyle sneaker', 'https://images.puma.com/image/upload/f_auto,q_auto,b_rgb:fafafa,w_2000,h_2000/global/371116/01/sv01/fnd/PNA/fmt/png/Cali-Court-Womens-Sneakers', 60.00, 60.00, 'USD', 'available', 'regular', 'women', 83, false, true, NOW()),
('model_puma_clyde_all_pro', 'brand_puma', 'cat_performance_sports', 'Clyde All-Pro', '194039', 'Basketball shoe with ProFoam+ cushioning', 'https://images.puma.com/image/upload/f_auto,q_auto,b_rgb:fafafa,w_2000,h_2000/global/194039/01/sv01/fnd/PNA/fmt/png/Clyde-All-Pro-Basketball-Shoes', 120.00, 120.00, 'USD', 'available', 'regular', 'unisex', 81, false, true, NOW()),
('model_puma_mayze', 'brand_puma', 'cat_everyday_casual', 'Mayze', '380784', 'Platform sneaker with stacked sole', 'https://images.puma.com/image/upload/f_auto,q_auto,b_rgb:fafafa,w_2000,h_2000/global/380784/01/sv01/fnd/PNA/fmt/png/Mayze-Womens-Sneakers', 80.00, 80.00, 'USD', 'available', 'regular', 'women', 87, false, true, NOW()),
('model_puma_fast_r_nitro_elite', 'brand_puma', 'cat_performance_sports', 'Fast-R Nitro Elite', '377218', 'Elite racing shoe with carbon fiber plate', 'https://images.puma.com/image/upload/f_auto,q_auto,b_rgb:fafafa,w_2000,h_2000/global/377218/01/sv01/fnd/PNA/fmt/png/Fast-R-NITRO-Elite-Mens-Running-Shoes', 250.00, 250.00, 'USD', 'available', 'regular', 'unisex', 84, false, true, NOW()),
('model_puma_mb_03', 'brand_puma', 'cat_performance_sports', 'MB.03', '378391', 'LaMelo Ball signature basketball shoe', 'https://images.puma.com/image/upload/f_auto,q_auto,b_rgb:fafafa,w_2000,h_2000/global/378391/01/sv01/fnd/PNA/fmt/png/MB.03-Basketball-Shoes', 125.00, 125.00, 'USD', 'available', 'regular', 'unisex', 89, true, true, NOW()),

-- TIER 2 BRANDS: Hoka Models (7 models)
('model_hoka_clifton_9', 'brand_hoka', 'cat_performance_sports', 'Clifton 9', '1127896', 'Lightweight daily trainer with maximum cushioning', 'https://www.hoka.com/dw/image/v2/BFQZ_PRD/on/demandware.static/-/Sites-hoka-master-catalog/default/dw8c8b8b8b/images/1127896/1127896_BBLC_1.jpg', 140.00, 140.00, 'USD', 'available', 'regular', 'unisex', 94, true, true, NOW()),
('model_hoka_bondi_8', 'brand_hoka', 'cat_specialty_comfort', 'Bondi 8', '1123202', 'Maximum cushioning for ultimate comfort', 'https://www.hoka.com/dw/image/v2/BFQZ_PRD/on/demandware.static/-/Sites-hoka-master-catalog/default/dw8c8b8b8b/images/1123202/1123202_BBLC_1.jpg', 165.00, 165.00, 'USD', 'available', 'regular', 'unisex', 92, true, true, NOW()),
('model_hoka_speedgoat_5', 'brand_hoka', 'cat_outdoor_hiking', 'Speedgoat 5', '1123157', 'Trail running shoe designed by Karl Meltzer', 'https://www.hoka.com/dw/image/v2/BFQZ_PRD/on/demandware.static/-/Sites-hoka-master-catalog/default/dw8c8b8b8b/images/1123157/1123157_BBLC_1.jpg', 155.00, 155.00, 'USD', 'available', 'regular', 'unisex', 89, false, true, NOW()),
('model_hoka_arahi_6', 'brand_hoka', 'cat_performance_sports', 'Arahi 6', '1123194', 'Stability shoe with J-Frame support', 'https://www.hoka.com/dw/image/v2/BFQZ_PRD/on/demandware.static/-/Sites-hoka-master-catalog/default/dw8c8b8b8b/images/1123194/1123194_BBLC_1.jpg', 140.00, 140.00, 'USD', 'available', 'regular', 'unisex', 86, false, true, NOW()),
('model_hoka_mach_5', 'brand_hoka', 'cat_performance_sports', 'Mach 5', '1127895', 'Lightweight and responsive for speed training', 'https://www.hoka.com/dw/image/v2/BFQZ_PRD/on/demandware.static/-/Sites-hoka-master-catalog/default/dw8c8b8b8b/images/1127895/1127895_BBLC_1.jpg', 130.00, 130.00, 'USD', 'available', 'regular', 'unisex', 87, false, true, NOW()),
('model_hoka_challenger_7', 'brand_hoka', 'cat_outdoor_hiking', 'Challenger 7', '1106525', 'Versatile trail shoe for varied terrain', 'https://www.hoka.com/dw/image/v2/BFQZ_PRD/on/demandware.static/-/Sites-hoka-master-catalog/default/dw8c8b8b8b/images/1106525/1106525_BBLC_1.jpg', 130.00, 130.00, 'USD', 'available', 'regular', 'unisex', 84, false, true, NOW()),
('model_hoka_recovery_slide', 'brand_hoka', 'cat_specialty_comfort', 'Recovery Slide', '1099673', 'Post-workout recovery slide with maximum cushioning', 'https://www.hoka.com/dw/image/v2/BFQZ_PRD/on/demandware.static/-/Sites-hoka-master-catalog/default/dw8c8b8b8b/images/1099673/1099673_BBLC_1.jpg', 60.00, 60.00, 'USD', 'available', 'regular', 'unisex', 88, false, true, NOW()),

-- TIER 2 BRANDS: Vans Models (6 models)
('model_vans_old_skool', 'brand_vans', 'cat_everyday_casual', 'Old Skool', 'VN000D3HY28', 'The original sidestripe shoe since 1977', 'https://images.vans.com/is/image/Vans/VN000D3HY28-HERO?$583x583$', 65.00, 65.00, 'USD', 'available', 'regular', 'unisex', 96, true, true, NOW()),
('model_vans_authentic', 'brand_vans', 'cat_everyday_casual', 'Authentic', 'VN000EE3BLK', 'The original and now iconic Vans style', 'https://images.vans.com/is/image/Vans/VN000EE3BLK-HERO?$583x583$', 50.00, 50.00, 'USD', 'available', 'regular', 'unisex', 94, true, true, NOW()),
('model_vans_sk8_hi', 'brand_vans', 'cat_everyday_casual', 'Sk8-Hi', 'VN000D5IB8C', 'High-top skate shoe with ankle support', 'https://images.vans.com/is/image/Vans/VN000D5IB8C-HERO?$583x583$', 70.00, 70.00, 'USD', 'available', 'regular', 'unisex', 92, false, true, NOW()),
('model_vans_slip_on', 'brand_vans', 'cat_everyday_casual', 'Classic Slip-On', 'VN000EYEBWW', 'Easy on, easy off with elastic side accents', 'https://images.vans.com/is/image/Vans/VN000EYEBWW-HERO?$583x583$', 55.00, 55.00, 'USD', 'available', 'regular', 'unisex', 90, false, true, NOW()),
('model_vans_era', 'brand_vans', 'cat_everyday_casual', 'Era', 'VN000EWZBLK', 'Low-top skate shoe with padded collar', 'https://images.vans.com/is/image/Vans/VN000EWZBLK-HERO?$583x583$', 55.00, 55.00, 'USD', 'available', 'regular', 'unisex', 88, false, true, NOW()),
('model_vans_ultrarange_exo', 'brand_vans', 'cat_everyday_casual', 'UltraRange EXO', 'VN0A4U1KBLK', 'All-terrain shoe with UltraCush midsole', 'https://images.vans.com/is/image/Vans/VN0A4U1KBLK-HERO?$583x583$', 80.00, 80.00, 'USD', 'available', 'regular', 'unisex', 85, false, true, NOW()),

-- TIER 2 BRANDS: Converse Models (6 models)
('model_converse_chuck_taylor_all_star', 'brand_converse', 'cat_everyday_casual', 'Chuck Taylor All Star', 'M9160C', 'The original basketball shoe since 1917', 'https://www.converse.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-cnv-master-catalog/default/dw8c8b8b8b/images/M9160C/M9160C_standard.jpg', 55.00, 55.00, 'USD', 'available', 'regular', 'unisex', 97, true, true, NOW()),
('model_converse_chuck_70', 'brand_converse', 'cat_everyday_casual', 'Chuck 70', '162050C', 'Premium version with enhanced comfort and durability', 'https://www.converse.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-cnv-master-catalog/default/dw8c8b8b8b/images/162050C/162050C_standard.jpg', 85.00, 85.00, 'USD', 'available', 'regular', 'unisex', 93, true, true, NOW()),
('model_converse_one_star', 'brand_converse', 'cat_everyday_casual', 'One Star', '158369C', 'Suede low-top with iconic star logo', 'https://www.converse.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-cnv-master-catalog/default/dw8c8b8b8b/images/158369C/158369C_standard.jpg', 70.00, 70.00, 'USD', 'available', 'regular', 'unisex', 86, false, true, NOW()),
('model_converse_run_star_hike', 'brand_converse', 'cat_everyday_casual', 'Run Star Hike', '166800C', 'Platform sneaker with jagged outsole', 'https://www.converse.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-cnv-master-catalog/default/dw8c8b8b8b/images/166800C/166800C_standard.jpg', 90.00, 90.00, 'USD', 'available', 'regular', 'unisex', 89, false, true, NOW()),
('model_converse_jack_purcell', 'brand_converse', 'cat_everyday_casual', 'Jack Purcell', '1Q698', 'Classic tennis shoe with smile toe cap', 'https://www.converse.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-cnv-master-catalog/default/dw8c8b8b8b/images/1Q698/1Q698_standard.jpg', 70.00, 70.00, 'USD', 'available', 'regular', 'unisex', 84, false, true, NOW()),
('model_converse_pro_leather', 'brand_converse', 'cat_everyday_casual', 'Pro Leather', '167237C', 'Basketball-inspired lifestyle sneaker', 'https://www.converse.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-cnv-master-catalog/default/dw8c8b8b8b/images/167237C/167237C_standard.jpg', 75.00, 75.00, 'USD', 'available', 'regular', 'unisex', 82, false, true, NOW()),

-- TIER 2 BRANDS: Merrell Models (6 models)
('model_merrell_moab_3', 'brand_merrell', 'cat_outdoor_hiking', 'Moab 3', 'J035781', 'Mother-of-all-boots hiking shoe with Vibram outsole', 'https://www.merrell.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-merrell-master-catalog/default/dw8c8b8b8b/images/J035781/J035781_1.jpg', 110.00, 110.00, 'USD', 'available', 'regular', 'unisex', 92, true, true, NOW()),
('model_merrell_trail_glove_7', 'brand_merrell', 'cat_outdoor_hiking', 'Trail Glove 7', 'J067183', 'Barefoot trail running shoe with Vibram outsole', 'https://www.merrell.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-merrell-master-catalog/default/dw8c8b8b8b/images/J067183/J067183_1.jpg', 100.00, 100.00, 'USD', 'available', 'regular', 'unisex', 85, false, true, NOW()),
('model_merrell_antora_3', 'brand_merrell', 'cat_outdoor_hiking', 'Antora 3', 'J067181', 'Trail running shoe with FloatPro foam', 'https://www.merrell.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-merrell-master-catalog/default/dw8c8b8b8b/images/J067181/J067181_1.jpg', 90.00, 90.00, 'USD', 'available', 'regular', 'unisex', 83, false, true, NOW()),
('model_merrell_jungle_moc', 'brand_merrell', 'cat_everyday_casual', 'Jungle Moc', 'J60787', 'Slip-on shoe with air cushion heel', 'https://www.merrell.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-merrell-master-catalog/default/dw8c8b8b8b/images/J60787/J60787_1.jpg', 95.00, 95.00, 'USD', 'available', 'regular', 'unisex', 89, false, true, NOW()),
('model_merrell_alverstone', 'brand_merrell', 'cat_outdoor_hiking', 'Alverstone', 'J035799', 'Waterproof hiking shoe with M Select DRY', 'https://www.merrell.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-merrell-master-catalog/default/dw8c8b8b8b/images/J035799/J035799_1.jpg', 85.00, 85.00, 'USD', 'available', 'regular', 'unisex', 81, false, true, NOW()),
('model_merrell_hydro_moc', 'brand_merrell', 'cat_specialty_comfort', 'Hydro Moc', 'J033370', 'Water-friendly recovery shoe', 'https://www.merrell.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-merrell-master-catalog/default/dw8c8b8b8b/images/J033370/J033370_1.jpg', 70.00, 70.00, 'USD', 'available', 'regular', 'unisex', 86, false, true, NOW()),

-- TIER 2 BRANDS: Salomon Models (6 models)
('model_salomon_speedcross_5', 'brand_salomon', 'cat_outdoor_hiking', 'Speedcross 5', '*********', 'Aggressive grip trail running shoe', 'https://www.salomon.com/sites/default/files/styles/product_image_zoom/public/2021-03/*********_0.jpg', 130.00, 130.00, 'USD', 'available', 'regular', 'unisex', 91, true, true, NOW()),
('model_salomon_xa_pro_3d_v8', 'brand_salomon', 'cat_outdoor_hiking', 'XA Pro 3D v8', '*********', 'Stable and protective trail running shoe', 'https://www.salomon.com/sites/default/files/styles/product_image_zoom/public/2021-03/*********_0.jpg', 130.00, 130.00, 'USD', 'available', 'regular', 'unisex', 89, false, true, NOW()),
('model_salomon_sense_ride_4', 'brand_salomon', 'cat_outdoor_hiking', 'Sense Ride 4', 'L41291000', 'Versatile trail running shoe for long distances', 'https://www.salomon.com/sites/default/files/styles/product_image_zoom/public/2021-03/L41291000_0.jpg', 130.00, 130.00, 'USD', 'available', 'regular', 'unisex', 87, false, true, NOW()),
('model_salomon_supercross_3', 'brand_salomon', 'cat_outdoor_hiking', 'Supercross 3', 'L41106000', 'Lightweight trail running shoe', 'https://www.salomon.com/sites/default/files/styles/product_image_zoom/public/2021-03/L41106000_0.jpg', 100.00, 100.00, 'USD', 'available', 'regular', 'unisex', 84, false, true, NOW()),
('model_salomon_x_ultra_4_gtx', 'brand_salomon', 'cat_outdoor_hiking', 'X Ultra 4 GTX', 'L41393000', 'Waterproof hiking shoe with Contagrip outsole', 'https://www.salomon.com/sites/default/files/styles/product_image_zoom/public/2021-03/L41393000_0.jpg', 150.00, 150.00, 'USD', 'available', 'regular', 'unisex', 88, false, true, NOW()),
('model_salomon_reelax_slide_5', 'brand_salomon', 'cat_specialty_comfort', 'Reelax Slide 5.0', 'L41279000', 'Recovery slide with EnergyCell+ midsole', 'https://www.salomon.com/sites/default/files/styles/product_image_zoom/public/2021-03/L41279000_0.jpg', 50.00, 50.00, 'USD', 'available', 'regular', 'unisex', 85, false, true, NOW()),

-- TIER 2 BRANDS: Skechers Models (6 models)
('model_skechers_max_cushioning_elite', 'brand_skechers', 'cat_specialty_comfort', 'Max Cushioning Elite', '220046', 'Ultra-cushioned walking shoe with Air-Cooled Memory Foam', 'https://www.skechers.com/dw/image/v2/BDCN_PRD/on/demandware.static/-/Sites-skechers-master/default/dw8c8b8b8b/images/220046/220046_BKW.jpg', 85.00, 85.00, 'USD', 'available', 'regular', 'unisex', 90, true, true, NOW()),
('model_skechers_go_walk_6', 'brand_skechers', 'cat_specialty_comfort', 'Go Walk 6', '216200', 'Lightweight walking shoe with Goga Max insole', 'https://www.skechers.com/dw/image/v2/BDCN_PRD/on/demandware.static/-/Sites-skechers-master/default/dw8c8b8b8b/images/216200/216200_BBK.jpg', 70.00, 70.00, 'USD', 'available', 'regular', 'unisex', 88, false, true, NOW()),
('model_skechers_go_run_elevate', 'brand_skechers', 'cat_performance_sports', 'Go Run Elevate', '220189', 'Running shoe with Hyper Burst cushioning', 'https://www.skechers.com/dw/image/v2/BDCN_PRD/on/demandware.static/-/Sites-skechers-master/default/dw8c8b8b8b/images/220189/220189_BKBL.jpg', 80.00, 80.00, 'USD', 'available', 'regular', 'unisex', 84, false, true, NOW()),
('model_skechers_arch_fit', 'brand_skechers', 'cat_specialty_comfort', 'Arch Fit', '232040', 'Podiatrist-certified arch support system', 'https://www.skechers.com/dw/image/v2/BDCN_PRD/on/demandware.static/-/Sites-skechers-master/default/dw8c8b8b8b/images/232040/232040_BKW.jpg', 75.00, 75.00, 'USD', 'available', 'regular', 'unisex', 87, false, true, NOW()),
('model_skechers_d_lites', 'brand_skechers', 'cat_everyday_casual', 'D\'Lites', '11931', 'Chunky retro sneaker with Air-Cooled Memory Foam', 'https://www.skechers.com/dw/image/v2/BDCN_PRD/on/demandware.static/-/Sites-skechers-master/default/dw8c8b8b8b/images/11931/11931_WSL.jpg', 65.00, 65.00, 'USD', 'available', 'regular', 'unisex', 86, false, true, NOW()),
('model_skechers_summits', 'brand_skechers', 'cat_everyday_casual', 'Summits', '12980', 'Slip-on sneaker with stretch-fit design', 'https://www.skechers.com/dw/image/v2/BDCN_PRD/on/demandware.static/-/Sites-skechers-master/default/dw8c8b8b8b/images/12980/12980_BBK.jpg', 60.00, 60.00, 'USD', 'available', 'regular', 'unisex', 83, false, true, NOW());

-- =====================================================
-- CONTINUE WITH PART 2 SCRIPT
-- =====================================================
-- Note: Execute comprehensive_shoe_population_part2.sql after this script
-- to complete the population with all Tier 3 brands and verification

-- =====================================================
-- QUICK VERIFICATION (Partial)
-- =====================================================

-- Check current progress
SELECT
    'Tier 1 & 2 Brands Populated' as status,
    COUNT(*) as models_so_far
FROM shoe_models;

SELECT
    c.name as category_name,
    COUNT(m.id) as current_model_count
FROM shoe_categories c
LEFT JOIN shoe_models m ON c.id = m.category_id
GROUP BY c.id, c.name
ORDER BY current_model_count DESC;
