/**
 * <PERSON>ript to execute comprehensive shoe database population
 * Uses service role key for admin operations to bypass RLS policies
 */

const { createClient } = require('@supabase/supabase-js');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// Load environment variables
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.error('Required: EXPO_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

// Create Supabase client with service role key (bypasses RLS)
const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function populateDirectly() {
  console.log('\n🔄 Populating database directly with structured data...');

  try {
    // Step 1: Check existing data
    console.log('🔍 Checking existing data...');
    const { data: existingCategories } = await supabase.from('shoe_categories').select('*');
    const { data: existingBrands } = await supabase.from('shoe_brands').select('*');
    const { data: existingModels } = await supabase.from('shoe_models').select('*');

    console.log(`📊 Current state: ${existingCategories?.length || 0} categories, ${existingBrands?.length || 0} brands, ${existingModels?.length || 0} models`);

    let insertedCategories = existingCategories || [];
    let insertedBrands = existingBrands || [];

    // Step 2: Insert categories if needed
    if (!existingCategories || existingCategories.length < 5) {
      console.log('📂 Inserting missing categories...');
      const categories = [
        { name: 'Performance Sports', description: 'High-performance athletic shoes for running, training, and sports', display_order: 1, icon_name: 'sports', color_theme: '#FF6B35', is_active: true },
        { name: 'Outdoor & Hiking', description: 'Durable shoes for outdoor activities, hiking, and trail running', display_order: 2, icon_name: 'mountain', color_theme: '#4CAF50', is_active: true },
        { name: 'Everyday Casual', description: 'Comfortable casual shoes for daily wear and lifestyle', display_order: 3, icon_name: 'casual', color_theme: '#2196F3', is_active: true },
        { name: 'Dress & Formal', description: 'Formal and dress shoes for professional and special occasions', display_order: 4, icon_name: 'formal', color_theme: '#9C27B0', is_active: true },
        { name: 'Specialty Comfort', description: 'Specialized comfort shoes with advanced cushioning and support', display_order: 5, icon_name: 'comfort', color_theme: '#FF9800', is_active: true }
      ];

      // Insert only missing categories
      for (const category of categories) {
        const exists = existingCategories?.find(c => c.name === category.name);
        if (!exists) {
          const { data: newCategory, error } = await supabase.from('shoe_categories').insert([category]).select();
          if (!error && newCategory) {
            insertedCategories.push(newCategory[0]);
          }
        }
      }
      console.log('✅ Categories ready:', insertedCategories.length);
    } else {
      console.log('✅ Categories already exist:', insertedCategories.length);
    }

    // Step 3: Insert brands if needed
    if (!existingBrands || existingBrands.length < 10) {
      console.log('🏷️  Inserting missing brands...');
      const brands = [
        { name: 'Nike', brand_description: 'Just Do It - Leading athletic footwear and apparel brand', country_origin: 'USA', website_url: 'https://www.nike.com', is_premium: true, is_active: true },
        { name: 'Adidas', brand_description: 'Impossible is Nothing - German multinational corporation', country_origin: 'Germany', website_url: 'https://www.adidas.com', is_premium: true, is_active: true },
        { name: 'New Balance', brand_description: 'Endorsed by No One - Premium athletic footwear', country_origin: 'USA', website_url: 'https://www.newbalance.com', is_premium: true, is_active: true },
        { name: 'ASICS', brand_description: 'Anima Sana In Corpore Sano - Japanese athletic equipment company', country_origin: 'Japan', website_url: 'https://www.asics.com', is_premium: true, is_active: true },
        { name: 'Brooks', brand_description: 'Run Happy - Specialized running shoe company', country_origin: 'USA', website_url: 'https://www.brooksrunning.com', is_premium: true, is_active: true },
        { name: 'Puma', brand_description: 'Forever Faster - German multinational corporation', country_origin: 'Germany', website_url: 'https://www.puma.com', is_premium: true, is_active: true },
        { name: 'Hoka', brand_description: 'Fly Human Fly - Maximalist running shoes', country_origin: 'France', website_url: 'https://www.hoka.com', is_premium: true, is_active: true },
        { name: 'Vans', brand_description: 'Off The Wall - Skateboarding shoes and lifestyle brand', country_origin: 'USA', website_url: 'https://www.vans.com', is_premium: false, is_active: true },
        { name: 'Converse', brand_description: 'All Star - Classic American shoe company', country_origin: 'USA', website_url: 'https://www.converse.com', is_premium: false, is_active: true },
        { name: 'Merrell', brand_description: 'Nothing Feels Better - Outdoor footwear company', country_origin: 'USA', website_url: 'https://www.merrell.com', is_premium: true, is_active: true }
      ];

      // Insert only missing brands
      for (const brand of brands) {
        const exists = existingBrands?.find(b => b.name === brand.name);
        if (!exists) {
          const { data: newBrand, error } = await supabase.from('shoe_brands').insert([brand]).select();
          if (!error && newBrand) {
            insertedBrands.push(newBrand[0]);
          }
        }
      }
      console.log('✅ Brands ready:', insertedBrands.length);
    } else {
      console.log('✅ Brands already exist:', insertedBrands.length);
    }

    return true;

  } catch (error) {
    console.error('❌ Error in direct population:', error.message);
    return false;
  }
}

async function populateDatabase() {
  try {
    console.log('🚀 Starting comprehensive shoe database population...');
    console.log(`📡 Connected to: ${supabaseUrl}`);

    // Test connection
    const { data: testData, error: testError } = await supabase
      .from('shoe_categories')
      .select('count', { count: 'exact', head: true });

    if (testError) {
      console.error('❌ Connection test failed:', testError.message);
      process.exit(1);
    }

    console.log('✅ Database connection verified');

    // Execute direct population
    const populationSuccess = await populateDirectly();

    if (!populationSuccess) {
      console.error('❌ Direct population failed, stopping execution');
      process.exit(1);
    }

    // Step 4: Create brand-category mappings
    console.log('🔗 Creating brand-category mappings...');
    const mappings = [];

    // Get current brands and categories for mapping
    const { data: currentBrands } = await supabase.from('shoe_brands').select('*');
    const { data: currentCategories } = await supabase.from('shoe_categories').select('*');

    // Find brands for mapping
    const nike = currentBrands?.find(b => b.name === 'Nike');
    const adidas = currentBrands?.find(b => b.name === 'Adidas');
    const vans = currentBrands?.find(b => b.name === 'Vans');

    const performanceSports = currentCategories?.find(c => c.name === 'Performance Sports');
    const everydayCasual = currentCategories?.find(c => c.name === 'Everyday Casual');

    if (nike && performanceSports) {
      mappings.push({ brand_id: nike.id, category_id: performanceSports.id, specialization_level: 5, is_primary: true });
    }
    if (adidas && performanceSports) {
      mappings.push({ brand_id: adidas.id, category_id: performanceSports.id, specialization_level: 5, is_primary: true });
    }
    if (vans && everydayCasual) {
      mappings.push({ brand_id: vans.id, category_id: everydayCasual.id, specialization_level: 5, is_primary: true });
    }

    if (mappings.length > 0) {
      const { error: mappingsError } = await supabase.from('brand_category_mapping').insert(mappings);
      if (mappingsError) {
        console.error('⚠️  Error inserting mappings:', mappingsError.message);
      } else {
        console.log('✅ Brand-category mappings created:', mappings.length);
      }
    }

    // Step 5: Add sample shoe models
    console.log('👟 Adding sample shoe models...');
    const sampleModels = [];

    if (nike && performanceSports) {
      sampleModels.push({
        brand_id: nike.id,
        category_id: performanceSports.id,
        model_name: 'Air Zoom Pegasus 40',
        model_code: 'DV3853',
        description: 'Responsive cushioning in the Pegasus 40 gives you a bouncy ride for everyday runs',
        image_url: 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/99486859-0ff7-4e79-b9e2-dfa4ee40699d/air-zoom-pegasus-40-mens-road-running-shoes-6C7ZhF.png',
        price_range_min: 130.00,
        price_range_max: 130.00,
        currency: 'USD',
        availability_status: 'available',
        fit_type: 'regular',
        target_gender: 'unisex',
        popularity_score: 95,
        is_featured: true,
        is_active: true
      });
    }

    if (adidas && performanceSports) {
      sampleModels.push({
        brand_id: adidas.id,
        category_id: performanceSports.id,
        model_name: 'Ultraboost 23',
        model_code: 'ID8011',
        description: 'Feel the energy return with every step in these running shoes',
        image_url: 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/b7d9211c26e7431aac24b0540fb3c00f/ultraboost-23-shoes.jpg',
        price_range_min: 190.00,
        price_range_max: 190.00,
        currency: 'USD',
        availability_status: 'available',
        fit_type: 'regular',
        target_gender: 'unisex',
        popularity_score: 93,
        is_featured: true,
        is_active: true
      });
    }

    if (vans && everydayCasual) {
      sampleModels.push({
        brand_id: vans.id,
        category_id: everydayCasual.id,
        model_name: 'Old Skool',
        model_code: 'VN000D3HY28',
        description: 'The original sidestripe shoe since 1977',
        image_url: 'https://images.vans.com/is/image/Vans/VN000D3HY28-HERO?$583x583$',
        price_range_min: 65.00,
        price_range_max: 65.00,
        currency: 'USD',
        availability_status: 'available',
        fit_type: 'regular',
        target_gender: 'unisex',
        popularity_score: 96,
        is_featured: true,
        is_active: true
      });
    }

    if (sampleModels.length > 0) {
      const { error: modelsError } = await supabase.from('shoe_models').insert(sampleModels);
      if (modelsError) {
        console.error('⚠️  Error inserting sample models:', modelsError.message);
      } else {
        console.log('✅ Sample models inserted:', sampleModels.length);
      }
    }
    
    // Final verification
    console.log('\n🔍 Final verification...');
    
    const { data: finalCategories } = await supabase.from('shoe_categories').select('*');
    const { data: finalBrands } = await supabase.from('shoe_brands').select('*');
    const { data: finalModels } = await supabase.from('shoe_models').select('*');
    const { data: finalMappings } = await supabase.from('brand_category_mapping').select('*');
    
    console.log('\n📊 FINAL RESULTS:');
    console.log(`✅ Categories: ${finalCategories?.length || 0}`);
    console.log(`✅ Brands: ${finalBrands?.length || 0}`);
    console.log(`✅ Models: ${finalModels?.length || 0}`);
    console.log(`✅ Brand-Category Mappings: ${finalMappings?.length || 0}`);

    if (finalModels && finalModels.length >= 3) {
      console.log('\n🎉 SUCCESS: Database populated with sample shoe models!');
      console.log('🎯 Your FootFit app now has a working shoe catalog');
      console.log('🚀 Ready for testing and further expansion');
    } else {
      console.log(`\n⚠️  Warning: Only ${finalModels?.length || 0} models populated`);
    }

    // Category breakdown
    if (finalCategories && finalModels) {
      console.log('\n📈 Category Distribution:');
      for (const category of finalCategories) {
        const categoryModels = finalModels.filter(m => m.category_id === category.id);
        console.log(`  ${category.name}: ${categoryModels.length} models`);
      }
    }

    // Brand breakdown
    if (finalBrands && finalModels) {
      console.log('\n🏷️  Brands with Models:');
      const brandCounts = finalBrands.map(brand => ({
        name: brand.name,
        count: finalModels.filter(m => m.brand_id === brand.id).length
      })).filter(brand => brand.count > 0);

      brandCounts.forEach(brand => {
        console.log(`  ${brand.name}: ${brand.count} models`);
      });
    }
    
  } catch (error) {
    console.error('❌ Fatal error during database population:', error);
    process.exit(1);
  }
}

// Execute the population
populateDatabase();
