/**
 * <PERSON><PERSON><PERSON> to populate the Supabase database with real shoe data
 * Run this script to set up the complete shoe database
 */

import { createClient } from '@supabase/supabase-js';

// Load environment variables
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY; // Use service role key for admin operations

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  console.error('Required: EXPO_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function populateDatabase() {
  try {
    console.log('🚀 Starting database population...');

    // Step 1: Clear existing data
    console.log('🧹 Clearing existing data...');
    await supabase.from('brand_category_mapping').delete().neq('brand_id', '');
    await supabase.from('shoe_models').delete().neq('id', '');
    await supabase.from('shoe_categories').delete().neq('id', '');
    await supabase.from('shoe_brands').delete().neq('id', '');

    // Step 2: Insert categories
    console.log('📂 Inserting shoe categories...');
    const categories = [
      {
        id: 'cat_performance_sports',
        name: 'Performance Sports',
        description: 'High-performance athletic shoes for running, training, and sports'
      },
      {
        id: 'cat_outdoor_hiking',
        name: 'Outdoor & Hiking',
        description: 'Durable shoes for outdoor activities, hiking, and trail running'
      },
      {
        id: 'cat_everyday_casual',
        name: 'Everyday Casual',
        description: 'Comfortable casual shoes for daily wear and lifestyle'
      },
      {
        id: 'cat_dress_formal',
        name: 'Dress & Formal',
        description: 'Formal and dress shoes for professional and special occasions'
      },
      {
        id: 'cat_specialty_comfort',
        name: 'Specialty Comfort',
        description: 'Specialized comfort shoes with advanced cushioning and support'
      }
    ];

    const { error: categoriesError } = await supabase
      .from('shoe_categories')
      .insert(categories);

    if (categoriesError) {
      console.error('Error inserting categories:', categoriesError);
      throw categoriesError;
    }

    // Step 3: Insert brands
    console.log('🏷️ Inserting shoe brands...');
    const brands = [
      // Performance Sports Brands
      { id: 'brand_nike', name: 'Nike', description: 'Leading athletic footwear and apparel company', website: 'https://www.nike.com' },
      { id: 'brand_adidas', name: 'Adidas', description: 'German multinational corporation that designs and manufactures shoes', website: 'https://www.adidas.com' },
      { id: 'brand_asics', name: 'ASICS', description: 'Japanese multinational corporation which produces sports equipment', website: 'https://www.asics.com' },
      { id: 'brand_new_balance', name: 'New Balance', description: 'American multinational corporation that manufactures sports footwear', website: 'https://www.newbalance.com' },

      // Outdoor & Hiking Brands
      { id: 'brand_merrell', name: 'Merrell', description: 'American manufacturing company of footwear products', website: 'https://www.merrell.com' },
      { id: 'brand_salomon', name: 'Salomon', description: 'French sports equipment manufacturing company', website: 'https://www.salomon.com' },
      { id: 'brand_columbia', name: 'Columbia', description: 'American company that manufactures and distributes outerwear', website: 'https://www.columbia.com' },
      { id: 'brand_keen', name: 'Keen', description: 'American footwear and accessories company', website: 'https://www.keenfootwear.com' },

      // Everyday Casual Brands
      { id: 'brand_vans', name: 'Vans', description: 'American manufacturer of skateboarding shoes and related apparel', website: 'https://www.vans.com' },
      { id: 'brand_converse', name: 'Converse', description: 'American shoe company that designs, distributes, and licenses sneakers', website: 'https://www.converse.com' },
      { id: 'brand_puma', name: 'Puma', description: 'German multinational corporation that designs and manufactures athletic shoes', website: 'https://www.puma.com' },

      // Dress & Formal Brands
      { id: 'brand_cole_haan', name: 'Cole Haan', description: 'American brand of mens and womens footwear and accessories', website: 'https://www.colehaan.com' },
      { id: 'brand_clarks', name: 'Clarks', description: 'British-based, international shoe manufacturer and retailer', website: 'https://www.clarks.com' },
      { id: 'brand_johnston_murphy', name: 'Johnston & Murphy', description: 'American footwear and clothing company', website: 'https://www.johnstonmurphy.com' },

      // Specialty Comfort Brands
      { id: 'brand_allbirds', name: 'Allbirds', description: 'New Zealand-American company that sells footwear and clothing', website: 'https://www.allbirds.com' },
      { id: 'brand_hoka', name: 'Hoka', description: 'Athletic shoe company that designs and markets running shoes', website: 'https://www.hoka.com' },
      { id: 'brand_brooks', name: 'Brooks', description: 'American sports equipment company that designs and markets high-performance running shoes', website: 'https://www.brooksrunning.com' },
      { id: 'brand_skechers', name: 'Skechers', description: 'American multinational footwear company', website: 'https://www.skechers.com' }
    ];

    const { error: brandsError } = await supabase
      .from('shoe_brands')
      .insert(brands);

    if (brandsError) {
      console.error('Error inserting brands:', brandsError);
      throw brandsError;
    }

    // Step 4: Insert brand-category mappings
    console.log('🔗 Inserting brand-category mappings...');
    const mappings = [
      // Performance Sports
      { brand_id: 'brand_nike', category_id: 'cat_performance_sports' },
      { brand_id: 'brand_adidas', category_id: 'cat_performance_sports' },
      { brand_id: 'brand_asics', category_id: 'cat_performance_sports' },
      { brand_id: 'brand_new_balance', category_id: 'cat_performance_sports' },

      // Outdoor & Hiking
      { brand_id: 'brand_merrell', category_id: 'cat_outdoor_hiking' },
      { brand_id: 'brand_salomon', category_id: 'cat_outdoor_hiking' },
      { brand_id: 'brand_columbia', category_id: 'cat_outdoor_hiking' },
      { brand_id: 'brand_keen', category_id: 'cat_outdoor_hiking' },

      // Everyday Casual
      { brand_id: 'brand_vans', category_id: 'cat_everyday_casual' },
      { brand_id: 'brand_converse', category_id: 'cat_everyday_casual' },
      { brand_id: 'brand_puma', category_id: 'cat_everyday_casual' },
      { brand_id: 'brand_adidas', category_id: 'cat_everyday_casual' }, // Adidas also makes casual shoes

      // Dress & Formal
      { brand_id: 'brand_cole_haan', category_id: 'cat_dress_formal' },
      { brand_id: 'brand_clarks', category_id: 'cat_dress_formal' },
      { brand_id: 'brand_johnston_murphy', category_id: 'cat_dress_formal' },

      // Specialty Comfort
      { brand_id: 'brand_allbirds', category_id: 'cat_specialty_comfort' },
      { brand_id: 'brand_hoka', category_id: 'cat_specialty_comfort' },
      { brand_id: 'brand_brooks', category_id: 'cat_specialty_comfort' },
      { brand_id: 'brand_skechers', category_id: 'cat_specialty_comfort' },
      { brand_id: 'brand_new_balance', category_id: 'cat_specialty_comfort' } // New Balance also makes comfort shoes
    ];

    const { error: mappingsError } = await supabase
      .from('brand_category_mapping')
      .insert(mappings);

    if (mappingsError) {
      console.error('Error inserting mappings:', mappingsError);
      throw mappingsError;
    }

    // Step 5: Insert shoe models
    console.log('👟 Inserting shoe models...');
    const sizeRange = {
      min_uk: 6,
      max_uk: 13,
      available_sizes: [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]
    };

    const shoeModels = [
      // Performance Sports Models
      {
        id: 'model_nike_pegasus40',
        brand_id: 'brand_nike',
        category_id: 'cat_performance_sports',
        name: 'Air Zoom Pegasus 40',
        description: 'Responsive cushioning in the Pegasus 40 gives you a bouncy ride for everyday runs',
        price_usd: 130.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/99486859-0ff7-4e79-b9e2-dfa4ee40699d/air-zoom-pegasus-40-mens-road-running-shoes-6C7ZhF.png'
      },
      {
        id: 'model_adidas_ultraboost23',
        brand_id: 'brand_adidas',
        category_id: 'cat_performance_sports',
        name: 'Ultraboost 23',
        description: 'Feel the energy return with every step in these adidas Ultraboost 23 running shoes',
        price_usd: 190.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/fbaf991a8b9544688e5cad7800abcec6_9366/Ultraboost_23_Shoes_Black_HQ6038_01_standard.jpg'
      },
      {
        id: 'model_asics_kayano30',
        brand_id: 'brand_asics',
        category_id: 'cat_performance_sports',
        name: 'Gel-Kayano 30',
        description: 'Experience adaptive stability and premium comfort that stands the test of time',
        price_usd: 160.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://images.asics.com/is/image/asics/1011B440_001_SR_RT_GLB?$zoom$'
      },
      {
        id: 'model_nb_fresh_foam',
        brand_id: 'brand_new_balance',
        category_id: 'cat_performance_sports',
        name: 'Fresh Foam X 1080v12',
        description: 'The most cushioned shoe in our running lineup, designed for comfort over any distance',
        price_usd: 150.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://nb.scene7.com/is/image/NB/m1080k12_nb_02_i?$pdpflexf2$&wid=440&hei=440'
      },

      // Outdoor & Hiking Models
      {
        id: 'model_merrell_moab3',
        brand_id: 'brand_merrell',
        category_id: 'cat_outdoor_hiking',
        name: 'Moab 3 Hiking Shoe',
        description: 'The next generation of the worlds most loved hiking shoe',
        price_usd: 110.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://www.merrell.com/dw/image/v2/ABCM_PRD/on/demandware.static/-/Sites-merrell-master-catalog/default/dw8c8b8c8c/images/J033691_1.jpg'
      },
      {
        id: 'model_salomon_xultra4',
        brand_id: 'brand_salomon',
        category_id: 'cat_outdoor_hiking',
        name: 'X Ultra 4 GTX',
        description: 'Lightweight hiking shoe with GORE-TEX protection and advanced chassis',
        price_usd: 150.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://www.salomon.com/sites/default/files/styles/product_image_zoom/public/2022-03/L41393000_0.jpg'
      },
      {
        id: 'model_columbia_newton',
        brand_id: 'brand_columbia',
        category_id: 'cat_outdoor_hiking',
        name: 'Newton Ridge Plus II',
        description: 'Waterproof hiking boot with superior traction and comfort',
        price_usd: 90.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://columbia.scene7.com/is/image/ColumbiaSportswear2/BM3970_231_f?wid=768&hei=806&v=1&fmt=jpeg&qlt=85,1&op_sharpen=0&resMode=sharp2&op_usm=1,1,6,0&iccEmbed=0'
      },
      {
        id: 'model_keen_targhee',
        brand_id: 'brand_keen',
        category_id: 'cat_outdoor_hiking',
        name: 'Targhee III Waterproof',
        description: 'All-terrain waterproof hiking shoe with KEEN.DRY technology',
        price_usd: 135.00,
        size_range: sizeRange,
        fit_type: 'wide',
        availability: 'available',
        image_url: 'https://www.keenfootwear.com/dw/image/v2/BDBM_PRD/on/demandware.static/-/Sites-keen-master-catalog/default/dw8c8b8c8c/images/products/1017783_1.jpg'
      },

      // Everyday Casual Models
      {
        id: 'model_vans_oldskool',
        brand_id: 'brand_vans',
        category_id: 'cat_everyday_casual',
        name: 'Old Skool',
        description: 'The classic skate shoe and first to bare the iconic sidestripe',
        price_usd: 65.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://images.vans.com/is/image/Vans/VN000D3HY28-HERO?$583x583$'
      },
      {
        id: 'model_converse_chuck',
        brand_id: 'brand_converse',
        category_id: 'cat_everyday_casual',
        name: 'Chuck Taylor All Star',
        description: 'The original basketball shoe that started it all',
        price_usd: 55.00,
        size_range: sizeRange,
        fit_type: 'narrow',
        availability: 'available',
        image_url: 'https://www.converse.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-cnv-master-catalog/default/dw8c8b8c8c/images/a_107/M9160_A_107X1.jpg'
      },
      {
        id: 'model_adidas_stansmith',
        brand_id: 'brand_adidas',
        category_id: 'cat_everyday_casual',
        name: 'Stan Smith',
        description: 'Clean and simple tennis-inspired shoes with a timeless look',
        price_usd: 80.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/4e894c2b8e8f4c6c8b5cad7800abcec6_9366/Stan_Smith_Shoes_White_FX5500_01_standard.jpg'
      },
      {
        id: 'model_puma_suede',
        brand_id: 'brand_puma',
        category_id: 'cat_everyday_casual',
        name: 'Suede Classic XXI',
        description: 'The legendary PUMA Suede with a modern twist',
        price_usd: 70.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://images.puma.com/image/upload/f_auto,q_auto,b_rgb:fafafa,w_2000,h_2000/global/374915/25/sv01/fnd/PNA/fmt/png/Suede-Classic-XXI-Sneakers'
      },

      // Dress & Formal Models
      {
        id: 'model_cole_haan_grand',
        brand_id: 'brand_cole_haan',
        category_id: 'cat_dress_formal',
        name: 'Grand Crosscourt II',
        description: 'Modern dress sneaker with Grand.OS technology for all-day comfort',
        price_usd: 150.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://www.colehaan.com/dw/image/v2/AALO_PRD/on/demandware.static/-/Sites-colehaan-master-catalog/default/dw8c8b8c8c/images/products/C29411_BLACK_1.jpg'
      },
      {
        id: 'model_clarks_desert',
        brand_id: 'brand_clarks',
        category_id: 'cat_dress_formal',
        name: 'Desert Boot',
        description: 'The original desert boot with crepe sole and premium suede upper',
        price_usd: 130.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://www.clarks.com/dw/image/v2/ABCM_PRD/on/demandware.static/-/Sites-clarks-master-catalog/default/dw8c8b8c8c/images/products/26138221_1.jpg'
      },
      {
        id: 'model_johnston_xc4',
        brand_id: 'brand_johnston_murphy',
        category_id: 'cat_dress_formal',
        name: 'XC4 Waterproof Golf Shoe',
        description: 'Waterproof dress shoe with athletic comfort technology',
        price_usd: 180.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://www.johnstonmurphy.com/dw/image/v2/ABCM_PRD/on/demandware.static/-/Sites-johnstonmurphy-master-catalog/default/dw8c8b8c8c/images/products/25-2490_BLACK_1.jpg'
      },

      // Specialty Comfort Models
      {
        id: 'model_allbirds_tree',
        brand_id: 'brand_allbirds',
        category_id: 'cat_specialty_comfort',
        name: 'Tree Runners',
        description: 'Sustainable running shoes made from eucalyptus tree fiber',
        price_usd: 98.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://cdn.allbirds.com/image/fetch/q_auto,f_auto/w_834,f_auto,q_auto,b_rgb:f5f5f5/https://cdn.allbirds.com/image/upload/f_auto,q_auto/v1/production/colorway/en-US/images/6Jm8mVrKHbNVGjQKIiG7yL/1'
      },
      {
        id: 'model_hoka_bondi8',
        brand_id: 'brand_hoka',
        category_id: 'cat_specialty_comfort',
        name: 'Bondi 8',
        description: 'Maximum cushioned running shoe for ultimate comfort',
        price_usd: 165.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://www.hoka.com/dw/image/v2/ABCM_PRD/on/demandware.static/-/Sites-hoka-master-catalog/default/dw8c8b8c8c/images/products/1123202_BBLC_1.jpg'
      },
      {
        id: 'model_brooks_glycerin',
        brand_id: 'brand_brooks',
        category_id: 'cat_specialty_comfort',
        name: 'Glycerin 21',
        description: 'Supreme softness with nitrogen-infused DNA LOFT v3 cushioning',
        price_usd: 150.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://www.brooksrunning.com/dw/image/v2/ABCM_PRD/on/demandware.static/-/Sites-brooks-master-catalog/default/dw8c8b8c8c/images/products/110393_1D_001_1.jpg'
      },
      {
        id: 'model_skechers_max',
        brand_id: 'brand_skechers',
        category_id: 'cat_specialty_comfort',
        name: 'Max Cushioning Elite',
        description: 'Ultra-lightweight with maximum responsive cushioning',
        price_usd: 85.00,
        size_range: sizeRange,
        fit_type: 'wide',
        availability: 'available',
        image_url: 'https://www.skechers.com/dw/image/v2/BDCN_PRD/on/demandware.static/-/Sites-skechers-master-catalog/default/dw8c8b8c8c/images/large/220069_BKW.jpg'
      },
      {
        id: 'model_nb_more_v4',
        brand_id: 'brand_new_balance',
        category_id: 'cat_specialty_comfort',
        name: 'Fresh Foam More v4',
        description: 'Ultra-plush cushioning for maximum comfort on any run',
        price_usd: 140.00,
        size_range: sizeRange,
        fit_type: 'regular',
        availability: 'available',
        image_url: 'https://nb.scene7.com/is/image/NB/mmorv4_nb_02_i?$pdpflexf2$&wid=440&hei=440'
      }
    ];

    // Insert shoe models in batches to avoid timeout
    const batchSize = 5;
    for (let i = 0; i < shoeModels.length; i += batchSize) {
      const batch = shoeModels.slice(i, i + batchSize);
      const { error: modelsError } = await supabase
        .from('shoe_models')
        .insert(batch);

      if (modelsError) {
        console.error(`Error inserting shoe models batch ${i / batchSize + 1}:`, modelsError);
        throw modelsError;
      }
      console.log(`✅ Inserted batch ${i / batchSize + 1} of shoe models`);
    }

    console.log('✅ Database population completed successfully!');
    console.log(`📊 Inserted: ${categories.length} categories, ${brands.length} brands, ${mappings.length} mappings, ${shoeModels.length} shoe models`);

  } catch (error) {
    console.error('❌ Error populating database:', error);
    process.exit(1);
  }
}

// Run the script
populateDatabase();
