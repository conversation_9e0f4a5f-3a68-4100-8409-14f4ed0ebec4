import { supabase } from '@/lib/supabase';
import { AuthError, Session, User } from '@supabase/supabase-js';
import React, { createContext, useCallback, useContext, useEffect, useState } from 'react';

interface Profile {
  id: string;
  email: string;
  full_name: string | null;
  avatar_url: string | null;
  preferred_brands: string[] | null;
  preferred_unit: 'cm' | 'inches';
  created_at: string;
  updated_at: string;
}

interface AuthContextType {
  session: Session | null;
  user: User | null;
  profile: Profile | null;
  loading: boolean;
  signUp: (email: string, password: string, fullName?: string) => Promise<{ error: AuthError | null }>;
  signIn: (email: string, password: string) => Promise<{ error: AuthError | null }>;
  signOut: () => Promise<{ error: AuthError | null }>;
  updateProfile: (updates: Partial<Profile>) => Promise<{ error: Error | null }>;
  refreshProfile: () => Promise<void>;
}

// Create a default context value to prevent undefined errors
const defaultAuthContext: AuthContextType = {
  session: null,
  user: null,
  profile: null,
  loading: true,
  signUp: async () => ({ error: null }),
  signIn: async () => ({ error: null }),
  signOut: async () => ({ error: null }),
  updateProfile: async () => ({ error: new Error('AuthProvider not initialized') }),
  refreshProfile: async () => {},
};

const AuthContext = createContext<AuthContextType>(defaultAuthContext);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<Profile | null>(null);
  const [loading, setLoading] = useState(true);

  const fetchProfile = useCallback(async (userId: string, userEmail?: string, userMetadata?: any, retryCount = 0) => {
    try {
      setLoading(true);
      const { log } = await import('@/utils/logger');
      log.info('Fetching user profile', 'AuthContext', { userId, attempt: retryCount + 1 });

      // Add timeout to prevent hanging
      const profilePromise = supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Profile fetch timeout')), 15000);
      });

      const { data, error } = await Promise.race([profilePromise, timeoutPromise]) as any;

      if (error) {
        log.error('Error fetching profile from Supabase', 'AuthContext', {
          error: error.message,
          code: error.code,
          details: error.details,
          hint: error.hint,
          userId
        });

        // If profile doesn't exist, create a basic one
        if (error.code === 'PGRST116') { // No rows returned
          log.info('Profile not found, creating basic profile', 'AuthContext', { userId });
          setProfile({
            id: userId,
            email: userEmail || '',
            full_name: userMetadata?.full_name || null,
            avatar_url: null,
            preferred_brands: null,
            preferred_unit: 'cm',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });
        } else if (error.message?.includes('timeout')) {
          // Handle timeout specifically
          log.warn('Profile fetch timed out, using basic profile', 'AuthContext', { userId });
          setProfile({
            id: userId,
            email: userEmail || '',
            full_name: userMetadata?.full_name || null,
            avatar_url: null,
            preferred_brands: null,
            preferred_unit: 'cm',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          });
        } else {
          throw error; // Re-throw for retry logic
        }
      } else {
        log.info('Profile fetched successfully', 'AuthContext', { userId, profileData: data });
        setProfile(data);
      }
    } catch (error) {
      const { log } = await import('@/utils/logger');
      log.error('Failed to fetch profile', 'AuthContext', {
        error: error instanceof Error ? error.message : 'Unknown error',
        userId,
        attempt: retryCount + 1
      });

      // Retry logic for network issues
      if (retryCount < 2 && (error instanceof Error && error.message.includes('timeout'))) {
        log.info('Retrying profile fetch', 'AuthContext', {
          userId,
          attempt: retryCount + 2,
          error: error.message
        });

        // Wait before retry
        setTimeout(() => {
          fetchProfile(userId, userEmail, userMetadata, retryCount + 1);
        }, 1000 * (retryCount + 1)); // Exponential backoff
        return;
      }

      // Create a basic profile on any error to prevent infinite loading
      setProfile({
        id: userId,
        email: userEmail || '',
        full_name: userMetadata?.full_name || null,
        avatar_url: null,
        preferred_brands: null,
        preferred_unit: 'cm',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString(),
      });
    } finally {
      setLoading(false);
    }
  }, []); // Remove user dependency to prevent circular updates

  useEffect(() => {
    let isMounted = true;

    // Get initial session with timeout and retry logic
    const initializeAuth = async (retryCount = 0) => {
      try {
        const { log } = await import('@/utils/logger');
        log.info('Initializing authentication', 'AuthContext', { attempt: retryCount + 1 });

        const sessionPromise = supabase.auth.getSession();
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Session fetch timeout')), 10000);
        });

        const { data: { session } } = await Promise.race([sessionPromise, timeoutPromise]) as any;

        if (!isMounted) return;

        setSession(session);
        setUser(session?.user ?? null);

        if (session?.user) {
          await fetchProfile(session.user.id, session.user.email, session.user.user_metadata);
        } else {
          setLoading(false);
        }
      } catch (error) {
        const { log } = await import('@/utils/logger');
        log.error('Failed to initialize authentication', 'AuthContext', error);

        // Retry logic for network issues with improved conditions
        if (retryCount < 3 && isMounted && error instanceof Error) {
          const isRetryableError =
            error.message.includes('timeout') ||
            error.message.includes('network') ||
            error.message.includes('fetch') ||
            error.message.includes('connection') ||
            error.message.includes('Session fetch timeout');

          if (isRetryableError) {
            log.info('Retrying authentication initialization', 'AuthContext', {
              attempt: retryCount + 2,
              error: error.message,
              maxRetries: 3,
              isRetryable: true
            });

            // Progressive backoff: 1s, 2s, 4s
            const backoffDelay = Math.min(1000 * Math.pow(2, retryCount), 4000);
            setTimeout(() => {
              if (isMounted) {
                initializeAuth(retryCount + 1);
              }
            }, backoffDelay);
            return;
          } else {
            log.warn('Non-retryable authentication error', 'AuthContext', {
              error: error.message,
              retryCount: retryCount + 1
            });
          }
        }

        if (isMounted) {
          setSession(null);
          setUser(null);
          setProfile(null);
          setLoading(false);
        }
      }
    };

    initializeAuth();

    // Listen for auth changes
    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange(async (event, session) => {
      if (!isMounted) return;

      const { log } = await import('@/utils/logger');
      log.debug(`Auth state changed: ${event}`, 'AuthContext', { userEmail: session?.user?.email });

      setSession(session);
      setUser(session?.user ?? null);

      if (session?.user) {
        await fetchProfile(session.user.id, session.user.email, session.user.user_metadata);
      } else {
        setProfile(null);
        setLoading(false);
      }
    });

    return () => {
      isMounted = false;
      subscription.unsubscribe();
    };
  }, [fetchProfile]);


  const signUp = async (email: string, password: string, fullName?: string) => {
    try {
      setLoading(true);


      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName,
          },
        },
      });

      if (error) {
        return { error };
      }
      // Profile will be created automatically via database trigger
      return { error: null };
    } catch (error) {
      return { error: error as AuthError };
    } finally {
      setLoading(false);
    }
  };

  const signIn = async (email: string, password: string) => {
    try {
      setLoading(true);


      const { error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      // Error handling is done by the auth state listener

      return { error };
    } catch (error) {
      return { error: error as AuthError };
    } finally {
      setLoading(false);
    }
  };

  const signOut = async () => {
    try {
      setLoading(true);
      const { error } = await supabase.auth.signOut();
      return { error };
    } catch (error) {
      return { error: error as AuthError };
    } finally {
      setLoading(false);
    }
  };

  const updateProfile = async (updates: Partial<Profile>) => {
    try {
      if (!user) {
        return { error: new Error('No user logged in') };
      }

      const { error } = await supabase
        .from('profiles')
        .update({
          ...updates,
          updated_at: new Date().toISOString(),
        })
        .eq('id', user.id);

      if (error) {
        return { error: new Error(error.message) };
      }

      // Refresh profile data
      await fetchProfile(user.id, user.email, user.user_metadata);
      return { error: null };
    } catch (error) {
      return { error: error as Error };
    }
  };

  const refreshProfile = async () => {
    if (user) {
      await fetchProfile(user.id, user.email, user.user_metadata);
    }
  };

  const value: AuthContextType = {
    session,
    user,
    profile,
    loading,
    signUp,
    signIn,
    signOut,
    updateProfile,
    refreshProfile,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  return context;
}

// Hook for protected routes
export function useRequireAuth() {
  const { user, loading } = useAuth();
  
  useEffect(() => {
    if (!loading && !user) {
      // Redirect to login screen
      // This will be handled by the navigation logic
    }
  }, [user, loading]);

  return { user, loading };
}
