/**
 * <PERSON><PERSON><PERSON> to populate the Supabase database with real shoe data
 * Run this script to set up the complete shoe database
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config();

// Load environment variables
const supabaseUrl = process.env.EXPO_PUBLIC_SUPABASE_URL;
const supabaseKey = process.env.SUPABASE_SERVICE_ROLE_KEY || process.env.EXPO_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseKey) {
  console.error('Missing Supabase environment variables');
  console.error('Required: EXPO_PUBLIC_SUPABASE_URL, and either SUPABASE_SERVICE_ROLE_KEY or EXPO_PUBLIC_SUPABASE_ANON_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function populateDatabase() {
  try {
    console.log('🚀 Starting database population...');

    // Step 1: Clear existing data
    console.log('🧹 Clearing existing data...');
    await supabase.from('brand_category_mapping').delete().neq('brand_id', '');
    await supabase.from('shoe_models').delete().neq('id', '');
    await supabase.from('shoe_categories').delete().neq('id', '');
    await supabase.from('shoe_brands').delete().neq('id', '');

    // Step 2: Insert categories
    console.log('📂 Inserting shoe categories...');
    const categories = [
      {
        name: 'Performance Sports',
        description: 'High-performance athletic shoes for running, training, and sports'
      },
      {
        name: 'Outdoor & Hiking',
        description: 'Durable shoes for outdoor activities, hiking, and trail running'
      },
      {
        name: 'Everyday Casual',
        description: 'Comfortable casual shoes for daily wear and lifestyle'
      },
      {
        name: 'Dress & Formal',
        description: 'Formal and dress shoes for professional and special occasions'
      },
      {
        name: 'Specialty Comfort',
        description: 'Specialized comfort shoes with advanced cushioning and support'
      }
    ];

    const { data: insertedCategories, error: categoriesError } = await supabase
      .from('shoe_categories')
      .insert(categories)
      .select();

    if (categoriesError) {
      console.error('Error inserting categories:', categoriesError);
      throw categoriesError;
    }

    // Step 3: Insert brands
    console.log('🏷️ Inserting shoe brands...');
    const brands = [
      // Performance Sports Brands
      { name: 'Nike', brand_description: 'Leading athletic footwear and apparel company', website_url: 'https://www.nike.com' },
      { name: 'Adidas', brand_description: 'German multinational corporation that designs and manufactures shoes', website_url: 'https://www.adidas.com' },
      { name: 'ASICS', brand_description: 'Japanese multinational corporation which produces sports equipment', website_url: 'https://www.asics.com' },
      { name: 'New Balance', brand_description: 'American multinational corporation that manufactures sports footwear', website_url: 'https://www.newbalance.com' },

      // Outdoor & Hiking Brands
      { name: 'Merrell', brand_description: 'American manufacturing company of footwear products', website_url: 'https://www.merrell.com' },
      { name: 'Salomon', brand_description: 'French sports equipment manufacturing company', website_url: 'https://www.salomon.com' },
      { name: 'Columbia', brand_description: 'American company that manufactures and distributes outerwear', website_url: 'https://www.columbia.com' },
      { name: 'Keen', brand_description: 'American footwear and accessories company', website_url: 'https://www.keenfootwear.com' },

      // Everyday Casual Brands
      { name: 'Vans', brand_description: 'American manufacturer of skateboarding shoes and related apparel', website_url: 'https://www.vans.com' },
      { name: 'Converse', brand_description: 'American shoe company that designs, distributes, and licenses sneakers', website_url: 'https://www.converse.com' },
      { name: 'Puma', brand_description: 'German multinational corporation that designs and manufactures athletic shoes', website_url: 'https://www.puma.com' },

      // Dress & Formal Brands
      { name: 'Cole Haan', brand_description: 'American brand of mens and womens footwear and accessories', website_url: 'https://www.colehaan.com' },
      { name: 'Clarks', brand_description: 'British-based, international shoe manufacturer and retailer', website_url: 'https://www.clarks.com' },
      { name: 'Johnston & Murphy', brand_description: 'American footwear and clothing company', website_url: 'https://www.johnstonmurphy.com' },

      // Specialty Comfort Brands
      { name: 'Allbirds', brand_description: 'New Zealand-American company that sells footwear and clothing', website_url: 'https://www.allbirds.com' },
      { name: 'Hoka', brand_description: 'Athletic shoe company that designs and markets running shoes', website_url: 'https://www.hoka.com' },
      { name: 'Brooks', brand_description: 'American sports equipment company that designs and markets high-performance running shoes', website_url: 'https://www.brooksrunning.com' },
      { name: 'Skechers', brand_description: 'American multinational footwear company', website_url: 'https://www.skechers.com' }
    ];

    const { data: insertedBrands, error: brandsError } = await supabase
      .from('shoe_brands')
      .insert(brands)
      .select();

    if (brandsError) {
      console.error('Error inserting brands:', brandsError);
      throw brandsError;
    }

    // Step 4: Insert brand-category mappings
    console.log('🔗 Inserting brand-category mappings...');

    // Create lookup maps for IDs
    const categoryMap = {};
    insertedCategories.forEach(cat => {
      categoryMap[cat.name] = cat.id;
    });

    const brandMap = {};
    insertedBrands.forEach(brand => {
      brandMap[brand.name] = brand.id;
    });

    const mappings = [
      // Performance Sports
      { brand_id: brandMap['Nike'], category_id: categoryMap['Performance Sports'] },
      { brand_id: brandMap['Adidas'], category_id: categoryMap['Performance Sports'] },
      { brand_id: brandMap['ASICS'], category_id: categoryMap['Performance Sports'] },
      { brand_id: brandMap['New Balance'], category_id: categoryMap['Performance Sports'] },

      // Outdoor & Hiking
      { brand_id: brandMap['Merrell'], category_id: categoryMap['Outdoor & Hiking'] },
      { brand_id: brandMap['Salomon'], category_id: categoryMap['Outdoor & Hiking'] },
      { brand_id: brandMap['Columbia'], category_id: categoryMap['Outdoor & Hiking'] },
      { brand_id: brandMap['Keen'], category_id: categoryMap['Outdoor & Hiking'] },

      // Everyday Casual
      { brand_id: brandMap['Vans'], category_id: categoryMap['Everyday Casual'] },
      { brand_id: brandMap['Converse'], category_id: categoryMap['Everyday Casual'] },
      { brand_id: brandMap['Puma'], category_id: categoryMap['Everyday Casual'] },
      { brand_id: brandMap['Adidas'], category_id: categoryMap['Everyday Casual'] }, // Adidas also makes casual shoes

      // Dress & Formal
      { brand_id: brandMap['Cole Haan'], category_id: categoryMap['Dress & Formal'] },
      { brand_id: brandMap['Clarks'], category_id: categoryMap['Dress & Formal'] },
      { brand_id: brandMap['Johnston & Murphy'], category_id: categoryMap['Dress & Formal'] },

      // Specialty Comfort
      { brand_id: brandMap['Allbirds'], category_id: categoryMap['Specialty Comfort'] },
      { brand_id: brandMap['Hoka'], category_id: categoryMap['Specialty Comfort'] },
      { brand_id: brandMap['Brooks'], category_id: categoryMap['Specialty Comfort'] },
      { brand_id: brandMap['Skechers'], category_id: categoryMap['Specialty Comfort'] },
      { brand_id: brandMap['New Balance'], category_id: categoryMap['Specialty Comfort'] } // New Balance also makes comfort shoes
    ];

    const { error: mappingsError } = await supabase
      .from('brand_category_mapping')
      .insert(mappings);

    if (mappingsError) {
      console.error('Error inserting mappings:', mappingsError);
      throw mappingsError;
    }

    console.log('✅ Database population completed successfully!');
    console.log(`📊 Inserted: ${categories.length} categories, ${brands.length} brands, ${mappings.length} mappings`);

  } catch (error) {
    console.error('❌ Error populating database:', error);
    process.exit(1);
  }
}

// Run the script
populateDatabase();
