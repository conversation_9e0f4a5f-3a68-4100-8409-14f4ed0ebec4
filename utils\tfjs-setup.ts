/**
 * TensorFlow.js Setup for React Native
 * 
 * This file initializes TensorFlow.js for React Native with the correct order of imports
 * and platform setup. Import this file before using TensorFlow.js in your app.
 */

import { log } from '@/utils/logger';

// Step 1: Import React Native platform (MUST be first)
import '@tensorflow/tfjs-react-native';

// Step 2: Import TensorFlow.js core
import * as tf from '@tensorflow/tfjs';

// Step 3: Import backends
import '@tensorflow/tfjs-backend-cpu';
import '@tensorflow/tfjs-backend-webgl';

// Step 4: Initialize platform
let isInitialized = false;

/**
 * Apply comprehensive TensorFlow.js polyfills
 */
async function applyTensorFlowJSPolyfills(): Promise<void> {
  try {
    log.info('Applying comprehensive TensorFlow.js polyfills', 'TensorFlowSetup');

    // Ensure util.isTypedArray is available in all possible locations
    const isTypedArrayFn = (value: any): boolean => {
      return value instanceof Int8Array ||
             value instanceof Uint8Array ||
             value instanceof Uint8ClampedArray ||
             value instanceof Int16Array ||
             value instanceof Uint16Array ||
             value instanceof Int32Array ||
             value instanceof Uint32Array ||
             value instanceof Float32Array ||
             value instanceof Float64Array ||
             value instanceof BigInt64Array ||
             value instanceof BigUint64Array;
    };

    // Patch TensorFlow.js environment directly
    const env = (tf as any).env();
    if (env && env.platform) {
      if (!env.platform.isTypedArray) {
        env.platform.isTypedArray = isTypedArrayFn;
        log.info('Patched tf.env().platform.isTypedArray', 'TensorFlowSetup');
      }
    }

    // Patch TensorFlow.js util module
    const tfUtil = (tf as any).util;
    if (tfUtil && !tfUtil.isTypedArray) {
      tfUtil.isTypedArray = isTypedArrayFn;
      log.info('Patched tf.util.isTypedArray', 'TensorFlowSetup');
    }

    // Patch TensorFlow.js backend
    const backend = tf.getBackend();
    if (backend) {
      const backendInstance = (tf as any).backend();
      if (backendInstance && !backendInstance.isTypedArray) {
        backendInstance.isTypedArray = isTypedArrayFn;
        log.info('Patched TensorFlow.js backend.isTypedArray', 'TensorFlowSetup');
      }
    }

    // Patch global util if needed
    if (typeof global !== 'undefined') {
      if (!global.util) {
        global.util = {};
      }
      if (!global.util.isTypedArray) {
        global.util.isTypedArray = isTypedArrayFn;
        log.info('Patched global.util.isTypedArray', 'TensorFlowSetup');
      }
    }

    // Patch TensorFlow.js tensor creation functions
    try {
      // Patch tensor creation utilities
      const tensorUtil = (tf as any).tensor_util;
      if (tensorUtil && !tensorUtil.isTypedArray) {
        tensorUtil.isTypedArray = isTypedArrayFn;
        log.info('Patched tf.tensor_util.isTypedArray', 'TensorFlowSetup');
      }

      // Patch engine utilities
      const engine = (tf as any).engine();
      if (engine && engine.util && !engine.util.isTypedArray) {
        engine.util.isTypedArray = isTypedArrayFn;
        log.info('Patched tf.engine().util.isTypedArray', 'TensorFlowSetup');
      }

      // Patch any other util locations
      const tfCore = (tf as any).core;
      if (tfCore && tfCore.util && !tfCore.util.isTypedArray) {
        tfCore.util.isTypedArray = isTypedArrayFn;
        log.info('Patched tf.core.util.isTypedArray', 'TensorFlowSetup');
      }
    } catch (error) {
      log.warn('Failed to patch some TensorFlow.js utilities', 'TensorFlowSetup', error);
    }

    log.info('TensorFlow.js polyfills applied successfully', 'TensorFlowSetup');
  } catch (error) {
    log.error('Failed to apply TensorFlow.js polyfills', 'TensorFlowSetup', error);
    throw error;
  }
}

/**
 * Initialize TensorFlow.js for React Native
 */
export async function setupTensorFlowJS(): Promise<boolean> {
  if (isInitialized) {
    return true;
  }

  try {
    log.info('Setting up TensorFlow.js for React Native', 'TensorFlowSetup');
    
    // Wait for TensorFlow.js to be ready
    await tf.ready();
    
    // Log platform info
    const backend = tf.getBackend();
    log.info('TensorFlow.js initialized successfully', 'TensorFlowSetup', {
      backend,
      version: tf.version.tfjs
    });
    
    // Apply comprehensive TensorFlow.js polyfills before testing
    await applyTensorFlowJSPolyfills();

    // Skip tensor test for now - focus on CNN model creation
    log.info('TensorFlow.js setup completed, skipping tensor test', 'TensorFlowSetup');
    
    isInitialized = true;
    return true;
  } catch (error) {
    log.error('Failed to initialize TensorFlow.js', 'TensorFlowSetup', error);
    return false;
  }
}

/**
 * Get TensorFlow.js instance
 */
export function getTF(): typeof tf {
  if (!isInitialized) {
    log.warn('TensorFlow.js not initialized, call setupTensorFlowJS() first', 'TensorFlowSetup');
  }
  return tf;
}

// Export TensorFlow.js instance
export { tf };

