#!/usr/bin/env python3
"""
FootFit Model Conversion Script
Converts the trained .h5 model to TensorFlow.js format for React Native integration
"""

import os
import sys
import json
import tensorflow as tf
import tensorflowjs as tfjs
from datetime import datetime

def convert_h5_to_tfjs():
    """Convert FootFit .h5 model to TensorFlow.js format"""
    
    print("🔄 FootFit Model Conversion to TensorFlow.js")
    print("=" * 45)
    
    # Paths
    h5_model_path = "ai-models/FootFit_Arch_Height_CNN.h5"
    tfjs_output_dir = "ai-models/tfjs_model"
    
    # Check if .h5 model exists
    if not os.path.exists(h5_model_path):
        print(f"❌ Error: {h5_model_path} not found")
        print("Please ensure the trained model file exists")
        return False
    
    print(f"📁 Input model: {h5_model_path}")
    print(f"📁 Output directory: {tfjs_output_dir}")
    
    try:
        # Load the .h5 model
        print("\n📥 Loading .h5 model...")
        model = tf.keras.models.load_model(h5_model_path)
        
        # Display model info
        print("✅ Model loaded successfully!")
        print(f"   📊 Total parameters: {model.count_params():,}")
        print(f"   📐 Input shape: {model.input_shape}")
        print(f"   📐 Output shape: {model.output_shape}")
        
        # Create output directory
        os.makedirs(tfjs_output_dir, exist_ok=True)
        
        # Convert to TensorFlow.js format
        print("\n🔄 Converting to TensorFlow.js format...")
        tfjs.converters.save_keras_model(
            model, 
            tfjs_output_dir,
            quantization_bytes=None,  # No quantization for best accuracy
            metadata={'name': 'FootFit_Arch_Height_CNN', 'version': '1.0'}
        )
        
        print("✅ Conversion completed successfully!")
        
        # Verify output files
        print("\n📋 Verifying output files:")
        expected_files = ['model.json', 'weights.bin']
        all_files_present = True
        
        for file in expected_files:
            file_path = os.path.join(tfjs_output_dir, file)
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"   ✅ {file}: {file_size:,} bytes")
            else:
                print(f"   ❌ {file}: Missing")
                all_files_present = False
        
        if all_files_present:
            print("\n🎉 Model conversion successful!")
            print("   Ready for React Native integration")
            
            # Create integration info
            integration_info = {
                "model_name": "FootFit_Arch_Height_CNN",
                "version": "1.0",
                "converted_at": datetime.now().isoformat(),
                "input_shape": list(model.input_shape),
                "output_shape": list(model.output_shape),
                "total_params": int(model.count_params()),
                "competitive_advantage": "arch_height_measurement",
                "usage": {
                    "load_path": "./ai-models/tfjs_model/model.json",
                    "input_format": "RGB image tensor [1, height, width, 3]",
                    "output_format": "[length_cm, width_cm, arch_height_cm]"
                }
            }
            
            # Save integration info
            info_path = os.path.join(tfjs_output_dir, "model_info.json")
            with open(info_path, 'w') as f:
                json.dump(integration_info, f, indent=2)
            
            print(f"   📄 Integration info saved: {info_path}")
            
            return True
        else:
            print("\n❌ Conversion failed - missing output files")
            return False
            
    except Exception as e:
        print(f"\n❌ Conversion failed: {str(e)}")
        print("   Check that TensorFlow and TensorFlow.js are properly installed")
        return False

def install_requirements():
    """Install required packages for conversion"""
    print("📦 Installing required packages...")
    
    try:
        import tensorflowjs
        print("✅ tensorflowjs already installed")
    except ImportError:
        print("📥 Installing tensorflowjs...")
        os.system("pip install tensorflowjs")
    
    try:
        import tensorflow
        print("✅ tensorflow already installed")
    except ImportError:
        print("📥 Installing tensorflow...")
        os.system("pip install tensorflow")

def main():
    """Main conversion function"""
    print("🚀 FootFit Model Conversion Script")
    print("==================================\n")
    
    # Check Python version
    if sys.version_info < (3, 7):
        print("❌ Python 3.7+ required")
        return
    
    print(f"✅ Python {sys.version}")
    
    # Install requirements
    install_requirements()
    
    # Convert model
    success = convert_h5_to_tfjs()
    
    if success:
        print("\n🎉 CONVERSION COMPLETE")
        print("=====================")
        print("Your FootFit model is now ready for React Native!")
        print("")
        print("📱 Next steps:")
        print("1. The model files are in ai-models/tfjs_model/")
        print("2. Test model loading in React Native")
        print("3. Integrate with foot analysis pipeline")
        print("4. Test end-to-end functionality")
        print("")
        print("🏗️ Competitive Advantage:")
        print("Your app now has real CNN-based arch height measurement!")
    else:
        print("\n❌ CONVERSION FAILED")
        print("===================")
        print("Please check the error messages above and try again.")

if __name__ == "__main__":
    main()
