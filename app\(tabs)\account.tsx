import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import { useCallback, useEffect, useState } from 'react';
import { Alert, ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { ThemeToggle } from '@/components/ThemeToggle';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingScreen } from '@/components/ui/Loading';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';

export default function AccountScreen() {
  const { colors } = useTheme();
  const { user, profile, signOut, loading: authLoading } = useAuth();

  const [userStats, setUserStats] = useState<{
    totalMeasurements: number;
    averageConfidence: number;
    mostRecentMeasurement: string | null;
  }>({
    totalMeasurements: 0,
    averageConfidence: 0,
    mostRecentMeasurement: null,
  });
  const [statsError, setStatsError] = useState<string | null>(null);
  const [statsLoading, setStatsLoading] = useState(false);

  // Load user stats with better error handling
  const loadUserStats = useCallback(async () => {
    if (!user || authLoading) return;

    try {
      setStatsLoading(true);
      setStatsError(null);

      const { SupabaseService } = await import('@/services/supabaseService');
      const stats = await SupabaseService.getUserStats(user.id);
      setUserStats(stats);
    } catch (error) {
      const { log } = await import('@/utils/logger');
      log.error('Failed to load user stats', 'AccountScreen', error);
      setStatsError('Unable to load account statistics. Please check your internet connection.');
    } finally {
      setStatsLoading(false);
    }
  }, [user, authLoading]);

  useEffect(() => {
    if (!authLoading && user) {
      loadUserStats();
    }
  }, [user, authLoading, loadUserStats]);



  const handleSignOut = async () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert(
      'Sign Out',
      'Are you sure you want to sign out?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Sign Out',
          style: 'destructive',
          onPress: async () => {
            Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
            const { error } = await signOut();
            if (error) {
              Alert.alert('Error', 'Failed to sign out. Please try again.');
            } else {
              router.replace('/auth/login');
            }
          },
        },
      ]
    );
  };

  const renderSettingItem = (
    icon: any,
    title: string,
    subtitle?: string,
    onPress?: () => void,
    rightComponent?: React.ReactNode
  ) => (
    <TouchableOpacity
      style={[styles.settingItem, { borderBottomColor: colors.border }]}
      onPress={onPress}
      disabled={!onPress}
    >
      <View style={styles.settingLeft}>
        <IconSymbol
          size={24}
          name={icon}
          color={colors.icon}
        />
        <View style={styles.settingText}>
          <ThemedText variant="labelLarge">{title}</ThemedText>
          {subtitle && (
            <ThemedText variant="caption" color="secondary">{subtitle}</ThemedText>
          )}
        </View>
      </View>
      {rightComponent || (
        onPress && (
          <IconSymbol
            size={20}
            name="chevron.right"
            color={colors.iconSecondary}
          />
        )
      )}
    </TouchableOpacity>
  );

  if (authLoading) {
    return <LoadingScreen text="Loading your profile..." />;
  }

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <ThemedView style={styles.header}>
        <View style={styles.headerLeft}>
          <IconSymbol
            size={32}
            name="person.fill"
            color={colors.primary}
          />
          <ThemedText variant="h2">Account</ThemedText>
        </View>
        <ThemeToggle size="medium" />
      </ThemedView>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Profile Section */}
        <ThemedView style={styles.section}>
          <ThemedView style={[styles.profileCard, { borderColor: colors.border }]}>
            <View style={styles.profileHeader}>
              <TouchableOpacity
                style={[styles.avatar, { backgroundColor: colors.primary }]}
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  Alert.alert(
                    'Profile Avatar',
                    'This avatar also appears in the homepage header and links to this account page.',
                    [{ text: 'Got it!' }]
                  );
                }}
                activeOpacity={0.8}
              >
                <ThemedText variant="h3" color="inverse">
                  {profile?.full_name
                    ? profile.full_name.split(' ').map(n => n[0]).join('').toUpperCase()
                    : user?.email?.charAt(0).toUpperCase() || 'U'
                  }
                </ThemedText>
              </TouchableOpacity>
              <View style={styles.profileInfo}>
                <ThemedText variant="h3">
                  {profile?.full_name || 'User'}
                </ThemedText>
                <ThemedText variant="body" color="secondary">
                  {user?.email || 'No email'}
                </ThemedText>
                <ThemedText variant="caption" color="tertiary">
                  Member since {profile?.created_at
                    ? new Date(profile.created_at).toLocaleDateString()
                    : 'Unknown'
                  }
                </ThemedText>
              </View>
            </View>

            {/* User Stats */}
            {statsError ? (
              <View style={styles.errorContainer}>
                <IconSymbol name="exclamationmark.triangle" size={24} color={colors.error} />
                <ThemedText variant="caption" style={[styles.errorText, { color: colors.error }]}>
                  {statsError}
                </ThemedText>
                <TouchableOpacity
                  style={[styles.retryButton, { backgroundColor: colors.primary }]}
                  onPress={() => {
                    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                    loadUserStats();
                  }}
                >
                  <ThemedText variant="caption" style={{ color: colors.background }}>
                    Retry
                  </ThemedText>
                </TouchableOpacity>
              </View>
            ) : statsLoading ? (
              <View style={styles.statsContainer}>
                <ThemedText variant="caption" color="secondary">
                  Loading statistics...
                </ThemedText>
              </View>
            ) : (
              <View style={styles.statsContainer}>
                <View style={styles.statItem}>
                  <ThemedText variant="h4" color="primary">
                    {userStats.totalMeasurements}
                  </ThemedText>
                  <ThemedText variant="caption" color="secondary">
                    Measurements
                  </ThemedText>
                </View>
                <View style={styles.statItem}>
                  <ThemedText variant="h4" color="primary">
                    {Math.round(userStats.averageConfidence * 100)}%
                  </ThemedText>
                  <ThemedText variant="caption" color="secondary">
                    Avg Confidence
                  </ThemedText>
                </View>
              </View>
            )}

            {/* Profile Details */}
            <View style={styles.profileDetails}>
              <View style={styles.profileDetailRow}>
                <ThemedText variant="labelLarge" color="secondary">
                  Preferred Unit:
                </ThemedText>
                <ThemedText variant="body">
                  {profile?.preferred_unit === 'cm' ? 'Centimeters' : 'Inches'}
                </ThemedText>
              </View>

              {profile?.preferred_brands && profile.preferred_brands.length > 0 && (
                <View style={styles.profileDetailColumn}>
                  <ThemedText variant="labelLarge" color="secondary" style={styles.detailLabel}>
                    Favorite Brands ({profile.preferred_brands.length}/5):
                  </ThemedText>
                  <View style={styles.favoriteBrands}>
                    {profile.preferred_brands.map((brand) => (
                      <View key={brand} style={[styles.brandTag, { backgroundColor: colors.primary + '20' }]}>
                        <ThemedText variant="caption" color="primary">
                          {brand}
                        </ThemedText>
                      </View>
                    ))}
                  </View>
                </View>
              )}


            </View>
          </ThemedView>
        </ThemedView>



        {/* Edit Profile Section */}
        <ThemedView style={styles.section}>
          <ThemedText variant="h4" style={styles.sectionTitle}>Profile Settings</ThemedText>
          <ThemedView style={[styles.settingsCard, { borderColor: colors.border }]}>
            {renderSettingItem(
              'person.crop.circle',
              'Edit Profile',
              'Update your personal information',
              () => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                console.log('Edit profile');
                router.push('/profile/edit');
              }
            )}
          </ThemedView>
        </ThemedView>



        {/* Logout Section */}
        <ThemedView style={styles.section}>
          <TouchableOpacity
            style={[styles.logoutButton, { borderColor: colors.error }]}
            onPress={handleSignOut}
          >
            <IconSymbol
              size={24}
              name="arrow.right.square"
              color={colors.error}
            />
            <ThemedText variant="labelLarge" color="error">Logout</ThemedText>
          </TouchableOpacity>
        </ThemedView>

        {/* App Info */}
        <ThemedView style={styles.footer}>
          <ThemedText variant="caption" color="tertiary" style={styles.appInfo}>
            FootFit v1.0.0
          </ThemedText>
        </ThemedView>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  headerLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    paddingBottom: 24,
  },
  sectionTitle: {
    marginBottom: 12,
  },
  profileCard: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 20,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 16,
  },
  avatar: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
  },
  profileInfo: {
    flex: 1,
    gap: 4,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-around',
    marginTop: 16,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  statItem: {
    alignItems: 'center',
    gap: 4,
  },
  settingsCard: {
    borderRadius: 12,
    borderWidth: 1,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: 16,
    borderBottomWidth: 1,
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    flex: 1,
  },
  settingText: {
    flex: 1,
    gap: 2,
  },
  logoutButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    gap: 12,
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  appInfo: {
    textAlign: 'center',
  },
  // Profile details styles
  profileDetails: {
    marginTop: 20,
    paddingTop: 20,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0, 0, 0, 0.1)',
    gap: 16,
  },
  profileDetailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  profileDetailColumn: {
    gap: 8,
  },
  detailLabel: {
    marginBottom: 4,
  },
  favoriteBrands: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 8,
  },
  brandTag: {
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
  },
  errorContainer: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: 16,
    gap: 8,
    borderRadius: 8,
    backgroundColor: 'rgba(255, 0, 0, 0.05)',
  },
  errorText: {
    textAlign: 'center',
  },
  retryButton: {
    paddingHorizontal: 12,
    paddingVertical: 6,
    borderRadius: 6,
  },
});
