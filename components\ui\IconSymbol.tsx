// Fallback for using MaterialIcons on Android and web.

import MaterialIcons from '@expo/vector-icons/MaterialIcons';
import { SymbolViewProps, SymbolWeight } from 'expo-symbols';
import { ComponentProps } from 'react';
import { OpaqueColorValue, type StyleProp, type TextStyle } from 'react-native';

type IconMapping = Record<SymbolViewProps['name'], ComponentProps<typeof MaterialIcons>['name']>;
export type IconSymbolName = keyof typeof MAPPING;

/**
 * Add your SF Symbols to Material Icons mappings here.
 * - see Material Icons in the [Icons Directory](https://icons.expo.fyi).
 * - see SF Symbols in the [SF Symbols](https://developer.apple.com/sf-symbols/) app.
 */
const MAPPING = {
  'house.fill': 'home',
  'paperplane.fill': 'send',
  'chevron.left.forwardslash.chevron.right': 'code',
  'chevron.right': 'chevron-right',
  'ruler': 'straighten',
  'heart.fill': 'favorite',
  'paintbrush.fill': 'palette',
  'questionmark.circle.fill': 'help',
  'camera.fill': 'camera-alt',
  'speedometer': 'speed',
  'arrow.clockwise.circle.fill': 'refresh',
  'arrow.clockwise.circle': 'refresh',
  'person.crop.circle': 'account-circle',
  'person.fill': 'person',
  'lock.fill': 'lock',
  'arrow.right.square': 'exit-to-app',
  'trash.fill': 'delete',
  'gear': 'settings',
  'xmark': 'close',
  'camera.rotate': 'flip-camera-android',
  'bell.fill': 'notifications',
  'shield.fill': 'security',
  'doc.text.fill': 'description',
  'star.fill': 'star',
  'plus.circle.fill': 'add-circle',
  'minus.circle.fill': 'remove-circle',
  'checkmark.circle.fill': 'check-circle',
  'xmark.circle.fill': 'cancel',
  'info.circle.fill': 'info',
  'exclamationmark.triangle.fill': 'warning',
  'eye.fill': 'visibility',
  'eye.slash.fill': 'visibility-off',
  'square.and.arrow.up': 'share',
  'square.and.arrow.down': 'download',
  'folder.fill': 'folder',
  'calendar.fill': 'event',
  'clock.fill': 'schedule',
  'location.fill': 'location-on',
  'phone.fill': 'phone',
  'envelope.fill': 'email',
  'link': 'link',
  'wifi': 'wifi',
  'battery.100': 'battery-full',
  'moon.fill': 'brightness-2',
  'sun.max.fill': 'wb-sunny',
  'icloud.and.arrow.up': 'cloud-upload',
  'brain.head.profile': 'psychology',
  '1.circle': 'looks-one',
  '2.circle': 'looks-two',
  '3.circle': 'looks-3',
  'checkmark': 'check',
  'photo.on.rectangle': 'photo-library',
  // Additional icons for FootFit app
  'exclamationmark.triangle': 'warning',
  'camera': 'camera-alt',
  'questionmark.circle': 'help',
  'heart': 'favorite-border',
  'hourglass': 'hourglass-empty',
  'trash': 'delete',
  'xmark.circle': 'cancel',
  'line.3.horizontal.decrease.circle': 'filter-list',
  'clock': 'schedule',
  'figure.walk': 'directions-walk',
  'lightbulb.fill': 'lightbulb',
  'viewfinder': 'center-focus-strong',
  'camera.viewfinder': 'camera-alt',
  'figure.stand': 'accessibility',
  'key.fill': 'vpn-key',
  'chevron.left': 'chevron-left',
  'pencil': 'edit',
  'shoeprints.fill': 'directions-walk',
  'ruler.fill': 'straighten',
  'checkmark.seal.fill': 'verified',
  'tag.fill': 'local-offer',
  'arrow.clockwise': 'refresh',
  'photo.fill': 'photo',
  'exclamationmark.circle.fill': 'error',
  'briefcase.fill': 'work',
  'mountain.2.fill': 'terrain',
  'figure.run': 'directions-run',
} as const;

/**
 * An icon component that uses native SF Symbols on iOS, and Material Icons on Android and web.
 * This ensures a consistent look across platforms, and optimal resource usage.
 * Icon `name`s are based on SF Symbols and require manual mapping to Material Icons.
 */
export function IconSymbol({
  name,
  size = 24,
  color,
  style,
}: {
  name: IconSymbolName;
  size?: number;
  color: string | OpaqueColorValue;
  style?: StyleProp<TextStyle>;
  weight?: SymbolWeight;
}) {
  return <MaterialIcons color={color} size={size} name={MAPPING[name]} style={style} />;
}
