{"model_name": "FootFit_Arch_Height_CNN_Mock", "version": "1.0-mock", "created_at": "2025-07-17T12:53:11.746Z", "type": "mock_for_testing", "input_shape": [null, 224, 224, 3], "output_shape": [null, 3], "output_format": "[length_cm, width_cm, arch_height_cm]", "competitive_advantage": "arch_height_measurement", "usage": {"load_path": "./ai-models/tfjs_model/model.json", "input_format": "RGB image tensor [1, 224, 224, 3]", "preprocessing": "Resize to 224x224, normalize to [0,1]", "postprocessing": "Direct output [length, width, arch_height] in cm"}, "mock_info": {"purpose": "Testing CNN loading pipeline", "replace_with": "Real trained model from FootFit_Arch_Height_CNN.h5", "accuracy": "Mock model - no real predictions"}}