import { router } from 'expo-router';
import React, { useState } from 'react';
import { Alert, KeyboardAvoidingView, Platform, ScrollView, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { Input } from '@/components/ui/Input';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';

export default function LoginScreen() {
  const { colors } = useTheme();
  const { signIn, loading } = useAuth();
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [showPassword, setShowPassword] = useState(false);

  const handleLogin = async () => {
    if (!email.trim() || !password.trim()) {
      Alert.alert('Error', 'Please fill in all fields');
      return;
    }

    const { error } = await signIn(email.trim(), password);
    
    if (error) {
      Alert.alert('Login Failed', error.message);
    } else {
      // Navigation will be handled by auth state change
      router.replace('/(tabs)' as any);
    }
  };

  const handleSignUp = () => {
    router.push('/auth/signup');
  };

  const handleForgotPassword = () => {
    router.push('/auth/forgot-password');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      <KeyboardAvoidingView 
        style={styles.keyboardView}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView 
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Header */}
          <ThemedView style={styles.header}>
            <View style={[styles.logo, { backgroundColor: colors.primary }]}>
              <IconSymbol
                size={48}
                name="figure.walk"
                color={colors.textInverse}
              />
            </View>
            <ThemedText variant="h1" color="primary" style={styles.title}>
              FootFit
            </ThemedText>
            <ThemedText variant="body" color="secondary" style={styles.subtitle}>
              Welcome back! Sign in to continue measuring your feet.
            </ThemedText>
          </ThemedView>

          {/* Login Form */}
          <ThemedView style={styles.form}>
            <Input
              label="Email"
              placeholder="Enter your email"
              value={email}
              onChangeText={setEmail}
              keyboardType="email-address"
              autoCapitalize="none"
              autoComplete="email"
              leftIcon="envelope.fill"
              required
            />

            <Input
              label="Password"
              placeholder="Enter your password"
              value={password}
              onChangeText={setPassword}
              secureTextEntry={!showPassword}
              autoComplete="password"
              leftIcon="lock.fill"
              rightIcon={showPassword ? "eye.slash.fill" : "eye.fill"}
              onRightIconPress={() => setShowPassword(!showPassword)}
              required
            />

            <Button
              title="Sign In"
              onPress={handleLogin}
              variant="primary"
              size="large"
              loading={loading}
              disabled={loading}
              fullWidth
              style={styles.loginButton}
            />

            <Button
              title="Forgot Password?"
              onPress={handleForgotPassword}
              variant="ghost"
              size="medium"
              style={styles.forgotButton}
            />
          </ThemedView>

          {/* Sign Up Section */}
          <ThemedView style={styles.signupSection}>
            <ThemedText variant="body" color="secondary" style={styles.signupText}>
              Don&apos;t have an account?
            </ThemedText>
            <Button
              title="Create Account"
              onPress={handleSignUp}
              variant="outline"
              size="large"
              fullWidth
              icon="person.badge.plus"
            />
          </ThemedView>

          {/* Footer */}
          <ThemedView style={styles.footer}>
            <ThemedText variant="caption" color="tertiary" style={styles.footerText}>
              By signing in, you agree to our Terms of Service and Privacy Policy
            </ThemedText>
          </ThemedView>
        </ScrollView>
      </KeyboardAvoidingView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  keyboardView: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 24,
    paddingVertical: 32,
  },
  header: {
    alignItems: 'center',
    marginBottom: 48,
  },
  logo: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 16,
  },
  title: {
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
    maxWidth: 280,
  },
  form: {
    gap: 16,
    marginBottom: 32,
  },
  loginButton: {
    marginTop: 8,
  },
  forgotButton: {
    alignSelf: 'center',
  },
  signupSection: {
    gap: 16,
    marginBottom: 32,
  },
  signupText: {
    textAlign: 'center',
  },
  footer: {
    marginTop: 'auto',
    paddingTop: 24,
  },
  footerText: {
    textAlign: 'center',
    lineHeight: 20,
  },
});
