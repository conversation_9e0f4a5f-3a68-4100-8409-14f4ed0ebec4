import { LoadingScreen } from '@/components/ui/Loading';
import { useAuth } from '@/contexts/AuthContext';
import { router, useSegments } from 'expo-router';
import React, { useEffect } from 'react';

export function AuthGuard({ children }: { children: React.ReactNode }) {
  const { user, loading } = useAuth();
  const segments = useSegments();

  useEffect(() => {
    if (loading) return; // Don't do anything while loading

    const inAuthGroup = segments[0] === 'auth';

    if (!user && !inAuthGroup) {
      // Redirect to login if user is not authenticated and not in auth group
      router.replace('/auth/login');
    } else if (user && inAuthGroup) {
      // Redirect to main app if user is authenticated and in auth group
      router.replace('/(tabs)' as any);
    }
  }, [user, loading, segments]);

  // Show loading screen while checking authentication
  if (loading) {
    return <LoadingScreen text="Loading..." />;
  }

  return <>{children}</>;
}
