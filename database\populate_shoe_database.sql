-- FootFit Shoe Database Population Script
-- This script populates the Supabase database with real shoe data across all 5 categories
-- Run this script after setting up the database schema

-- Clear existing data (for fresh setup)
DELETE FROM brand_category_mapping;
DELETE FROM shoe_models;
DELETE FROM shoe_categories;
DELETE FROM shoe_brands;

-- Insert shoe categories (5 categories as specified)
INSERT INTO shoe_categories (id, name, description, created_at) VALUES
('cat_performance_sports', 'Performance Sports', 'High-performance athletic shoes for running, training, and sports', NOW()),
('cat_outdoor_hiking', 'Outdoor & Hiking', 'Durable shoes for outdoor activities, hiking, and trail running', NOW()),
('cat_everyday_casual', 'Everyday Casual', 'Comfortable casual shoes for daily wear and lifestyle', NOW()),
('cat_dress_formal', 'Dress & Formal', 'Formal and dress shoes for professional and special occasions', NOW()),
('cat_specialty_comfort', 'Specialty Comfort', 'Specialized comfort shoes with advanced cushioning and support', NOW());

-- Insert shoe brands
INSERT INTO shoe_brands (id, name, description, website, created_at) VALUES
-- Performance Sports Brands
('brand_nike', 'Nike', 'Leading athletic footwear and apparel company', 'https://www.nike.com', NOW()),
('brand_adidas', 'Adidas', 'German multinational corporation that designs and manufactures shoes', 'https://www.adidas.com', NOW()),
('brand_asics', 'ASICS', 'Japanese multinational corporation which produces sports equipment', 'https://www.asics.com', NOW()),
('brand_new_balance', 'New Balance', 'American multinational corporation that manufactures sports footwear', 'https://www.newbalance.com', NOW()),

-- Outdoor & Hiking Brands
('brand_merrell', 'Merrell', 'American manufacturing company of footwear products', 'https://www.merrell.com', NOW()),
('brand_salomon', 'Salomon', 'French sports equipment manufacturing company', 'https://www.salomon.com', NOW()),
('brand_columbia', 'Columbia', 'American company that manufactures and distributes outerwear', 'https://www.columbia.com', NOW()),
('brand_keen', 'Keen', 'American footwear and accessories company', 'https://www.keenfootwear.com', NOW()),

-- Everyday Casual Brands
('brand_vans', 'Vans', 'American manufacturer of skateboarding shoes and related apparel', 'https://www.vans.com', NOW()),
('brand_converse', 'Converse', 'American shoe company that designs, distributes, and licenses sneakers', 'https://www.converse.com', NOW()),
('brand_puma', 'Puma', 'German multinational corporation that designs and manufactures athletic shoes', 'https://www.puma.com', NOW()),

-- Dress & Formal Brands
('brand_cole_haan', 'Cole Haan', 'American brand of mens and womens footwear and accessories', 'https://www.colehaan.com', NOW()),
('brand_clarks', 'Clarks', 'British-based, international shoe manufacturer and retailer', 'https://www.clarks.com', NOW()),
('brand_johnston_murphy', 'Johnston & Murphy', 'American footwear and clothing company', 'https://www.johnstonmurphy.com', NOW()),

-- Specialty Comfort Brands
('brand_allbirds', 'Allbirds', 'New Zealand-American company that sells footwear and clothing', 'https://www.allbirds.com', NOW()),
('brand_hoka', 'Hoka', 'Athletic shoe company that designs and markets running shoes', 'https://www.hoka.com', NOW()),
('brand_brooks', 'Brooks', 'American sports equipment company that designs and markets high-performance running shoes', 'https://www.brooksrunning.com', NOW()),
('brand_skechers', 'Skechers', 'American multinational footwear company', 'https://www.skechers.com', NOW());

-- Insert brand-category mappings
INSERT INTO brand_category_mapping (brand_id, category_id, created_at) VALUES
-- Performance Sports
('brand_nike', 'cat_performance_sports', NOW()),
('brand_adidas', 'cat_performance_sports', NOW()),
('brand_asics', 'cat_performance_sports', NOW()),
('brand_new_balance', 'cat_performance_sports', NOW()),

-- Outdoor & Hiking
('brand_merrell', 'cat_outdoor_hiking', NOW()),
('brand_salomon', 'cat_outdoor_hiking', NOW()),
('brand_columbia', 'cat_outdoor_hiking', NOW()),
('brand_keen', 'cat_outdoor_hiking', NOW()),

-- Everyday Casual
('brand_vans', 'cat_everyday_casual', NOW()),
('brand_converse', 'cat_everyday_casual', NOW()),
('brand_puma', 'cat_everyday_casual', NOW()),
('brand_adidas', 'cat_everyday_casual', NOW()), -- Adidas also makes casual shoes

-- Dress & Formal
('brand_cole_haan', 'cat_dress_formal', NOW()),
('brand_clarks', 'cat_dress_formal', NOW()),
('brand_johnston_murphy', 'cat_dress_formal', NOW()),

-- Specialty Comfort
('brand_allbirds', 'cat_specialty_comfort', NOW()),
('brand_hoka', 'cat_specialty_comfort', NOW()),
('brand_brooks', 'cat_specialty_comfort', NOW()),
('brand_skechers', 'cat_specialty_comfort', NOW()),
('brand_new_balance', 'cat_specialty_comfort', NOW()); -- New Balance also makes comfort shoes

-- Insert shoe models with complete data
-- Performance Sports Models
INSERT INTO shoe_models (id, brand_id, category_id, name, description, price_usd, size_range, fit_type, availability, image_url, created_at) VALUES
('model_nike_pegasus40', 'brand_nike', 'cat_performance_sports', 'Air Zoom Pegasus 40', 'Responsive cushioning in the Pegasus 40 gives you a bouncy ride for everyday runs', 130.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/99486859-0ff7-4e79-b9e2-dfa4ee40699d/air-zoom-pegasus-40-mens-road-running-shoes-6C7ZhF.png', NOW()),

('model_adidas_ultraboost23', 'brand_adidas', 'cat_performance_sports', 'Ultraboost 23', 'Feel the energy return with every step in these adidas Ultraboost 23 running shoes', 190.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/fbaf991a8b9544688e5cad7800abcec6_9366/Ultraboost_23_Shoes_Black_HQ6038_01_standard.jpg', NOW()),

('model_asics_kayano30', 'brand_asics', 'cat_performance_sports', 'Gel-Kayano 30', 'Experience adaptive stability and premium comfort that stands the test of time', 160.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://images.asics.com/is/image/asics/1011B440_001_SR_RT_GLB?$zoom$', NOW()),

('model_nb_fresh_foam', 'brand_new_balance', 'cat_performance_sports', 'Fresh Foam X 1080v12', 'The most cushioned shoe in our running lineup, designed for comfort over any distance', 150.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://nb.scene7.com/is/image/NB/m1080k12_nb_02_i?$pdpflexf2$&wid=440&hei=440', NOW()),

-- Outdoor & Hiking Models
('model_merrell_moab3', 'brand_merrell', 'cat_outdoor_hiking', 'Moab 3 Hiking Shoe', 'The next generation of the worlds most loved hiking shoe', 110.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://www.merrell.com/dw/image/v2/ABCM_PRD/on/demandware.static/-/Sites-merrell-master-catalog/default/dw8c8b8c8c/images/J033691_1.jpg', NOW()),

('model_salomon_xultra4', 'brand_salomon', 'cat_outdoor_hiking', 'X Ultra 4 GTX', 'Lightweight hiking shoe with GORE-TEX protection and advanced chassis', 150.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://www.salomon.com/sites/default/files/styles/product_image_zoom/public/2022-03/L41393000_0.jpg', NOW()),

('model_columbia_newton', 'brand_columbia', 'cat_outdoor_hiking', 'Newton Ridge Plus II', 'Waterproof hiking boot with superior traction and comfort', 90.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://columbia.scene7.com/is/image/ColumbiaSportswear2/BM3970_231_f?wid=768&hei=806&v=1&fmt=jpeg&qlt=85,1&op_sharpen=0&resMode=sharp2&op_usm=1,1,6,0&iccEmbed=0', NOW()),

('model_keen_targhee', 'brand_keen', 'cat_outdoor_hiking', 'Targhee III Waterproof', 'All-terrain waterproof hiking shoe with KEEN.DRY technology', 135.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'wide', 'available', 'https://www.keenfootwear.com/dw/image/v2/BDBM_PRD/on/demandware.static/-/Sites-keen-master-catalog/default/dw8c8b8c8c/images/products/1017783_1.jpg', NOW()),

-- Everyday Casual Models
('model_vans_oldskool', 'brand_vans', 'cat_everyday_casual', 'Old Skool', 'The classic skate shoe and first to bare the iconic sidestripe', 65.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://images.vans.com/is/image/Vans/VN000D3HY28-HERO?$583x583$', NOW()),

('model_converse_chuck', 'brand_converse', 'cat_everyday_casual', 'Chuck Taylor All Star', 'The original basketball shoe that started it all', 55.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'narrow', 'available', 'https://www.converse.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-cnv-master-catalog/default/dw8c8b8c8c/images/a_107/M9160_A_107X1.jpg', NOW()),

('model_adidas_stansmith', 'brand_adidas', 'cat_everyday_casual', 'Stan Smith', 'Clean and simple tennis-inspired shoes with a timeless look', 80.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/4e894c2b8e8f4c6c8b5cad7800abcec6_9366/Stan_Smith_Shoes_White_FX5500_01_standard.jpg', NOW()),

('model_puma_suede', 'brand_puma', 'cat_everyday_casual', 'Suede Classic XXI', 'The legendary PUMA Suede with a modern twist', 70.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://images.puma.com/image/upload/f_auto,q_auto,b_rgb:fafafa,w_2000,h_2000/global/374915/25/sv01/fnd/PNA/fmt/png/Suede-Classic-XXI-Sneakers', NOW()),

-- Dress & Formal Models
('model_cole_haan_grand', 'brand_cole_haan', 'cat_dress_formal', 'Grand Crosscourt II', 'Modern dress sneaker with Grand.OS technology for all-day comfort', 150.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://www.colehaan.com/dw/image/v2/AALO_PRD/on/demandware.static/-/Sites-colehaan-master-catalog/default/dw8c8b8c8c/images/products/C29411_BLACK_1.jpg', NOW()),

('model_clarks_desert', 'brand_clarks', 'cat_dress_formal', 'Desert Boot', 'The original desert boot with crepe sole and premium suede upper', 130.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://www.clarks.com/dw/image/v2/ABCM_PRD/on/demandware.static/-/Sites-clarks-master-catalog/default/dw8c8b8c8c/images/products/26138221_1.jpg', NOW()),

('model_johnston_xc4', 'brand_johnston_murphy', 'cat_dress_formal', 'XC4 Waterproof Golf Shoe', 'Waterproof dress shoe with athletic comfort technology', 180.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://www.johnstonmurphy.com/dw/image/v2/ABCM_PRD/on/demandware.static/-/Sites-johnstonmurphy-master-catalog/default/dw8c8b8c8c/images/products/25-2490_BLACK_1.jpg', NOW()),

-- Specialty Comfort Models
('model_allbirds_tree', 'brand_allbirds', 'cat_specialty_comfort', 'Tree Runners', 'Sustainable running shoes made from eucalyptus tree fiber', 98.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://cdn.allbirds.com/image/fetch/q_auto,f_auto/w_834,f_auto,q_auto,b_rgb:f5f5f5/https://cdn.allbirds.com/image/upload/f_auto,q_auto/v1/production/colorway/en-US/images/6Jm8mVrKHbNVGjQKIiG7yL/1', NOW()),

('model_hoka_bondi8', 'brand_hoka', 'cat_specialty_comfort', 'Bondi 8', 'Maximum cushioned running shoe for ultimate comfort', 165.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://www.hoka.com/dw/image/v2/ABCM_PRD/on/demandware.static/-/Sites-hoka-master-catalog/default/dw8c8b8c8c/images/products/1123202_BBLC_1.jpg', NOW()),

('model_brooks_glycerin', 'brand_brooks', 'cat_specialty_comfort', 'Glycerin 21', 'Supreme softness with nitrogen-infused DNA LOFT v3 cushioning', 150.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://www.brooksrunning.com/dw/image/v2/ABCM_PRD/on/demandware.static/-/Sites-brooks-master-catalog/default/dw8c8b8c8c/images/products/110393_1D_001_1.jpg', NOW()),

('model_skechers_max', 'brand_skechers', 'cat_specialty_comfort', 'Max Cushioning Elite', 'Ultra-lightweight with maximum responsive cushioning', 85.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'wide', 'available', 'https://www.skechers.com/dw/image/v2/BDCN_PRD/on/demandware.static/-/Sites-skechers-master-catalog/default/dw8c8b8c8c/images/large/220069_BKW.jpg', NOW()),

('model_nb_more_v4', 'brand_new_balance', 'cat_specialty_comfort', 'Fresh Foam More v4', 'Ultra-plush cushioning for maximum comfort on any run', 140.00, '{"min_uk": 6, "max_uk": 13, "available_sizes": [6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11, 11.5, 12, 12.5, 13]}', 'regular', 'available', 'https://nb.scene7.com/is/image/NB/mmorv4_nb_02_i?$pdpflexf2$&wid=440&hei=440', NOW());

-- Add success message
SELECT 'Shoe database populated successfully with ' || COUNT(*) || ' models across 5 categories' as result
FROM shoe_models;
