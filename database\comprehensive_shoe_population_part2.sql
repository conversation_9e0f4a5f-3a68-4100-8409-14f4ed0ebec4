-- FootFit Comprehensive Shoe Database Population Script - Part 2
-- TIER 3 BRANDS (3-4 models each) and completion
-- This is a continuation of comprehensive_shoe_population.sql

-- TIER 3 BRANDS: Under Armour Models (4 models)
INSERT INTO shoe_models (id, brand_id, category_id, model_name, model_code, description, image_url, price_range_min, price_range_max, currency, availability_status, fit_type, target_gender, popularity_score, is_featured, is_active, created_at) VALUES
('model_ua_hovr_phantom_3', 'brand_under_armour', 'cat_performance_sports', 'HOVR Phantom 3', '3024269', 'Connected running shoe with UA HOVR cushioning', 'https://underarmour.scene7.com/is/image/Underarmour/3024269-001_DEFAULT?rp=standard-30pad|pdpMainDesktop&scl=1&fmt=jpg&qlt=85&resMode=sharp2&cache=on,on&bgc=f0f0f0&wid=566&hei=708', 140.00, 140.00, 'USD', 'available', 'regular', 'unisex', 85, false, true, NOW()),
('model_ua_charged_bandit_7', 'brand_under_armour', 'cat_performance_sports', 'Charged Bandit 7', '3023858', 'Lightweight running shoe with Charged Cushioning', 'https://underarmour.scene7.com/is/image/Underarmour/3023858-001_DEFAULT?rp=standard-30pad|pdpMainDesktop&scl=1&fmt=jpg&qlt=85&resMode=sharp2&cache=on,on&bgc=f0f0f0&wid=566&hei=708', 70.00, 70.00, 'USD', 'available', 'regular', 'unisex', 82, false, true, NOW()),
('model_ua_curry_flow_10', 'brand_under_armour', 'cat_performance_sports', 'Curry Flow 10', '3025917', 'Stephen Curry signature basketball shoe', 'https://underarmour.scene7.com/is/image/Underarmour/3025917-001_DEFAULT?rp=standard-30pad|pdpMainDesktop&scl=1&fmt=jpg&qlt=85&resMode=sharp2&cache=on,on&bgc=f0f0f0&wid=566&hei=708', 160.00, 160.00, 'USD', 'available', 'regular', 'unisex', 88, true, true, NOW()),
('model_ua_project_rock_4', 'brand_under_armour', 'cat_performance_sports', 'Project Rock 4', '3023393', 'Dwayne Johnson signature training shoe', 'https://underarmour.scene7.com/is/image/Underarmour/3023393-001_DEFAULT?rp=standard-30pad|pdpMainDesktop&scl=1&fmt=jpg&qlt=85&resMode=sharp2&cache=on,on&bgc=f0f0f0&wid=566&hei=708', 140.00, 140.00, 'USD', 'available', 'regular', 'unisex', 86, false, true, NOW()),

-- TIER 3 BRANDS: Jordan Brand Models (4 models)
('model_jordan_air_jordan_1_low', 'brand_jordan', 'cat_performance_sports', 'Air Jordan 1 Low', '553558', 'Classic basketball shoe with iconic design', 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/b7d9211c-26e7-431a-ac24-b0540fb3c00f/air-jordan-1-low-shoes-6Q1tFM.png', 90.00, 110.00, 'USD', 'available', 'regular', 'unisex', 95, true, true, NOW()),
('model_jordan_air_jordan_4_retro', 'brand_jordan', 'cat_performance_sports', 'Air Jordan 4 Retro', '308497', 'Retro basketball shoe with visible Air cushioning', 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/b7d9211c-26e7-431a-ac24-b0540fb3c00f/air-jordan-4-retro-shoes-6Q1tFM.png', 200.00, 200.00, 'USD', 'available', 'regular', 'unisex', 93, true, true, NOW()),
('model_jordan_jumpman_mvp', 'brand_jordan', 'cat_performance_sports', 'Jumpman MVP', 'DO7447', 'Modern basketball shoe with Jumpman heritage', 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/b7d9211c-26e7-431a-ac24-b0540fb3c00f/jumpman-mvp-shoes-6Q1tFM.png', 85.00, 85.00, 'USD', 'available', 'regular', 'unisex', 87, false, true, NOW()),
('model_jordan_air_jordan_11_low', 'brand_jordan', 'cat_performance_sports', 'Air Jordan 11 Low', '528895', 'Low-top version of the iconic Jordan 11', 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/b7d9211c-26e7-431a-ac24-b0540fb3c00f/air-jordan-11-low-shoes-6Q1tFM.png', 185.00, 185.00, 'USD', 'available', 'regular', 'unisex', 91, false, true, NOW()),

-- TIER 3 BRANDS: Reebok Models (4 models)
('model_reebok_club_c_85', 'brand_reebok', 'cat_everyday_casual', 'Club C 85', 'AR0456', 'Classic tennis-inspired lifestyle sneaker', 'https://assets.reebok.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/b7d9211c26e7431aac24b0540fb3c00f/club-c-85-shoes.jpg', 75.00, 75.00, 'USD', 'available', 'regular', 'unisex', 89, false, true, NOW()),
('model_reebok_nano_x2', 'brand_reebok', 'cat_performance_sports', 'Nano X2', 'GZ0723', 'CrossFit training shoe with Floatride Energy foam', 'https://assets.reebok.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/b7d9211c26e7431aac24b0540fb3c00f/nano-x2-training-shoes.jpg', 130.00, 130.00, 'USD', 'available', 'regular', 'unisex', 84, false, true, NOW()),
('model_reebok_classic_leather', 'brand_reebok', 'cat_everyday_casual', 'Classic Leather', '49797', 'Timeless leather sneaker since 1983', 'https://assets.reebok.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/b7d9211c26e7431aac24b0540fb3c00f/classic-leather-shoes.jpg', 70.00, 70.00, 'USD', 'available', 'regular', 'unisex', 91, true, true, NOW()),
('model_reebok_floatride_energy_4', 'brand_reebok', 'cat_performance_sports', 'Floatride Energy 4', 'GZ8735', 'Daily running shoe with Floatride Energy foam', 'https://assets.reebok.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/b7d9211c26e7431aac24b0540fb3c00f/floatride-energy-4-running-shoes.jpg', 90.00, 90.00, 'USD', 'available', 'regular', 'unisex', 82, false, true, NOW()),

-- TIER 3 BRANDS: The North Face Models (4 models)
('model_tnf_vectiv_exploris', 'brand_the_north_face', 'cat_outdoor_hiking', 'VECTIV Exploris', 'NF0A4T2S', 'Lightweight hiking shoe with carbon fiber plate', 'https://images.thenorthface.com/is/image/TheNorthFace/NF0A4T2S_KX7_hero?$638x745$', 150.00, 150.00, 'USD', 'available', 'regular', 'unisex', 87, false, true, NOW()),
('model_tnf_ultra_fastpack_iv', 'brand_the_north_face', 'cat_outdoor_hiking', 'Ultra Fastpack IV', 'NF0A46B5', 'Fast hiking shoe for day hikes and approaches', 'https://images.thenorthface.com/is/image/TheNorthFace/NF0A46B5_KX7_hero?$638x745$', 130.00, 130.00, 'USD', 'available', 'regular', 'unisex', 84, false, true, NOW()),
('model_tnf_vectiv_infinite', 'brand_the_north_face', 'cat_outdoor_hiking', 'VECTIV Infinite', 'NF0A4T25', 'Trail running shoe with rocker midsole geometry', 'https://images.thenorthface.com/is/image/TheNorthFace/NF0A4T25_KX7_hero?$638x745$', 140.00, 140.00, 'USD', 'available', 'regular', 'unisex', 85, false, true, NOW()),
('model_tnf_thermoball_traction_bootie', 'brand_the_north_face', 'cat_specialty_comfort', 'ThermoBall Traction Bootie', 'NF0A331M', 'Insulated camp shoe for cold weather', 'https://images.thenorthface.com/is/image/TheNorthFace/NF0A331M_KX7_hero?$638x745$', 60.00, 60.00, 'USD', 'available', 'regular', 'unisex', 83, false, true, NOW()),

-- TIER 3 BRANDS: KEEN Models (4 models)
('model_keen_targhee_iii', 'brand_keen', 'cat_outdoor_hiking', 'Targhee III', '1017784', 'Waterproof hiking shoe with KEEN.DRY membrane', 'https://www.keenfootwear.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-keen-master-catalog/default/dw8c8b8b8b/images/1017784/1017784_1.jpg', 135.00, 135.00, 'USD', 'available', 'regular', 'unisex', 88, false, true, NOW()),
('model_keen_newport_h2', 'brand_keen', 'cat_outdoor_hiking', 'Newport H2', '1001907', 'Hybrid sandal for water and trail activities', 'https://www.keenfootwear.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-keen-master-catalog/default/dw8c8b8b8b/images/1001907/1001907_1.jpg', 105.00, 105.00, 'USD', 'available', 'regular', 'unisex', 86, false, true, NOW()),
('model_keen_uneek', 'brand_keen', 'cat_everyday_casual', 'UNEEK', '1014097', 'Innovative cord construction for adaptive fit', 'https://www.keenfootwear.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-keen-master-catalog/default/dw8c8b8b8b/images/1014097/1014097_1.jpg', 90.00, 90.00, 'USD', 'available', 'regular', 'unisex', 84, false, true, NOW()),
('model_keen_explore_vent', 'brand_keen', 'cat_outdoor_hiking', 'Explore Vent', '1025668', 'Breathable hiking shoe for warm weather', 'https://www.keenfootwear.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-keen-master-catalog/default/dw8c8b8b8b/images/1025668/1025668_1.jpg', 100.00, 100.00, 'USD', 'available', 'regular', 'unisex', 82, false, true, NOW()),

-- TIER 3 BRANDS: Columbia Models (3 models)
('model_columbia_redmond_iii', 'brand_columbia', 'cat_outdoor_hiking', 'Redmond III', '1865031', 'Waterproof hiking shoe with Omni-Tech membrane', 'https://www.columbia.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-columbia-master-catalog/default/dw8c8b8b8b/images/1865031/1865031_010_f.jpg', 70.00, 70.00, 'USD', 'available', 'regular', 'unisex', 81, false, true, NOW()),
('model_columbia_facet_75', 'brand_columbia', 'cat_outdoor_hiking', 'Facet 75', '1903071', 'Lightweight hiking shoe with Adapt Trax outsole', 'https://www.columbia.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-columbia-master-catalog/default/dw8c8b8b8b/images/1903071/1903071_010_f.jpg', 100.00, 100.00, 'USD', 'available', 'regular', 'unisex', 83, false, true, NOW()),
('model_columbia_sh_ft_outdry', 'brand_columbia', 'cat_outdoor_hiking', 'SH/FT OutDry', '1826361', 'Urban-outdoor hybrid with OutDry waterproofing', 'https://www.columbia.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-columbia-master-catalog/default/dw8c8b8b8b/images/1826361/1826361_010_f.jpg', 90.00, 90.00, 'USD', 'available', 'regular', 'unisex', 85, false, true, NOW()),

-- TIER 3 BRANDS: Allbirds Models (4 models)
('model_allbirds_tree_runners', 'brand_allbirds', 'cat_everyday_casual', 'Tree Runners', 'TR001', 'Sustainable sneaker made from eucalyptus tree fiber', 'https://cdn.allbirds.com/image/fetch/q_auto,f_auto/w_1000,h_1000,c_fill,g_auto/https://cdn.allbirds.com/image/upload/f_auto,q_auto/v1/production/colorway/en-US/images/TR001_SHOE_ANGLE_GLOBAL_MENS_TREE_RUNNER_NATURAL_WHITE.png', 98.00, 98.00, 'USD', 'available', 'regular', 'unisex', 89, true, true, NOW()),
('model_allbirds_wool_runners', 'brand_allbirds', 'cat_everyday_casual', 'Wool Runners', 'WR001', 'Cozy sneaker made from merino wool', 'https://cdn.allbirds.com/image/fetch/q_auto,f_auto/w_1000,h_1000,c_fill,g_auto/https://cdn.allbirds.com/image/upload/f_auto,q_auto/v1/production/colorway/en-US/images/WR001_SHOE_ANGLE_GLOBAL_MENS_WOOL_RUNNER_NATURAL_GREY.png', 98.00, 98.00, 'USD', 'available', 'regular', 'unisex', 91, true, true, NOW()),
('model_allbirds_tree_dashers', 'brand_allbirds', 'cat_performance_sports', 'Tree Dashers', 'TD001', 'Performance running shoe made from eucalyptus', 'https://cdn.allbirds.com/image/fetch/q_auto,f_auto/w_1000,h_1000,c_fill,g_auto/https://cdn.allbirds.com/image/upload/f_auto,q_auto/v1/production/colorway/en-US/images/TD001_SHOE_ANGLE_GLOBAL_MENS_TREE_DASHER_NATURAL_WHITE.png', 125.00, 125.00, 'USD', 'available', 'regular', 'unisex', 86, false, true, NOW()),
('model_allbirds_tree_loungers', 'brand_allbirds', 'cat_specialty_comfort', 'Tree Loungers', 'TL001', 'Slip-on comfort shoe made from eucalyptus', 'https://cdn.allbirds.com/image/fetch/q_auto,f_auto/w_1000,h_1000,c_fill,g_auto/https://cdn.allbirds.com/image/upload/f_auto,q_auto/v1/production/colorway/en-US/images/TL001_SHOE_ANGLE_GLOBAL_MENS_TREE_LOUNGER_NATURAL_WHITE.png', 95.00, 95.00, 'USD', 'available', 'regular', 'unisex', 88, false, true, NOW()),

-- TIER 3 BRANDS: Birkenstock Models (3 models)
('model_birkenstock_arizona', 'brand_birkenstock', 'cat_specialty_comfort', 'Arizona', '051791', 'Classic two-strap sandal with cork footbed', 'https://www.birkenstock.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-birkenstock-master-catalog/default/dw8c8b8b8b/images/051791/051791_1.jpg', 110.00, 110.00, 'USD', 'available', 'regular', 'unisex', 94, true, true, NOW()),
('model_birkenstock_boston', 'brand_birkenstock', 'cat_specialty_comfort', 'Boston', '060191', 'Closed-toe clog with adjustable strap', 'https://www.birkenstock.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-birkenstock-master-catalog/default/dw8c8b8b8b/images/060191/060191_1.jpg', 140.00, 140.00, 'USD', 'available', 'regular', 'unisex', 92, true, true, NOW()),
('model_birkenstock_gizeh', 'brand_birkenstock', 'cat_specialty_comfort', 'Gizeh', '043691', 'Thong sandal with toe post design', 'https://www.birkenstock.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-birkenstock-master-catalog/default/dw8c8b8b8b/images/043691/043691_1.jpg', 100.00, 100.00, 'USD', 'available', 'regular', 'unisex', 89, false, true, NOW()),

-- TIER 3 BRANDS: Dansko Models (3 models)
('model_dansko_professional', 'brand_dansko', 'cat_specialty_comfort', 'Professional', '206-020202', 'Classic clog for healthcare professionals', 'https://www.dansko.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-dansko-master-catalog/default/dw8c8b8b8b/images/206-020202/206-020202_1.jpg', 135.00, 135.00, 'USD', 'available', 'regular', 'unisex', 91, true, true, NOW()),
('model_dansko_xp_2', 'brand_dansko', 'cat_specialty_comfort', 'XP 2.0', '***********', 'Next-generation professional clog', 'https://www.dansko.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-dansko-master-catalog/default/dw8c8b8b8b/images/***********/***********_1.jpg', 140.00, 140.00, 'USD', 'available', 'regular', 'unisex', 88, false, true, NOW()),
('model_dansko_pace', 'brand_dansko', 'cat_specialty_comfort', 'Pace', '9435-471200', 'Athletic-inspired comfort shoe', 'https://www.dansko.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-dansko-master-catalog/default/dw8c8b8b8b/images/9435-471200/9435-471200_1.jpg', 120.00, 120.00, 'USD', 'available', 'regular', 'unisex', 85, false, true, NOW()),

-- TIER 3 BRANDS: OOFOS Models (3 models)
('model_oofos_ooriginal', 'brand_oofos', 'cat_specialty_comfort', 'OOriginal', '1000', 'Original recovery sandal with OOfoam technology', 'https://www.oofos.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-oofos-master-catalog/default/dw8c8b8b8b/images/1000/1000_BLACK_1.jpg', 60.00, 60.00, 'USD', 'available', 'regular', 'unisex', 90, true, true, NOW()),
('model_oofos_oomg', 'brand_oofos', 'cat_specialty_comfort', 'OOmg', '1500', 'Low shoe with OOfoam recovery technology', 'https://www.oofos.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-oofos-master-catalog/default/dw8c8b8b8b/images/1500/1500_BLACK_1.jpg', 120.00, 120.00, 'USD', 'available', 'regular', 'unisex', 87, false, true, NOW()),
('model_oofos_oocloog', 'brand_oofos', 'cat_specialty_comfort', 'OOcloog', '1400', 'Closed-toe recovery clog', 'https://www.oofos.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-oofos-master-catalog/default/dw8c8b8b8b/images/1400/1400_BLACK_1.jpg', 80.00, 80.00, 'USD', 'available', 'regular', 'unisex', 85, false, true, NOW()),

-- TIER 3 BRANDS: Cole Haan Models (4 models)
('model_cole_haan_zerogrand', 'brand_cole_haan', 'cat_dress_formal', 'ZeroGrand', 'C26471', 'Hybrid dress shoe with athletic sole', 'https://www.colehaan.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-colehaan-master-catalog/default/dw8c8b8b8b/images/C26471/C26471_1.jpg', 200.00, 200.00, 'USD', 'available', 'regular', 'men', 92, true, true, NOW()),
('model_cole_haan_grand_crosscourt', 'brand_cole_haan', 'cat_everyday_casual', 'Grand Crosscourt', 'C29411', 'Tennis-inspired lifestyle sneaker', 'https://www.colehaan.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-colehaan-master-catalog/default/dw8c8b8b8b/images/C29411/C29411_1.jpg', 120.00, 120.00, 'USD', 'available', 'regular', 'unisex', 88, false, true, NOW()),
('model_cole_haan_original_grand', 'brand_cole_haan', 'cat_dress_formal', 'Original Grand', 'C20278', 'Classic wingtip with Grand.OS technology', 'https://www.colehaan.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-colehaan-master-catalog/default/dw8c8b8b8b/images/C20278/C20278_1.jpg', 280.00, 280.00, 'USD', 'available', 'regular', 'men', 89, false, true, NOW()),
('model_cole_haan_generation_zerogrand', 'brand_cole_haan', 'cat_everyday_casual', 'Generation ZeroGrand', 'C33413', 'Modern sneaker with ZeroGrand technology', 'https://www.colehaan.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-colehaan-master-catalog/default/dw8c8b8b8b/images/C33413/C33413_1.jpg', 150.00, 150.00, 'USD', 'available', 'regular', 'unisex', 86, false, true, NOW()),

-- TIER 3 BRANDS: Clarks Models (4 models)
('model_clarks_desert_boot', 'brand_clarks', 'cat_dress_formal', 'Desert Boot', '26138221', 'Iconic ankle boot with crepe sole', 'https://www.clarks.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-clarks-master-catalog/default/dw8c8b8b8b/images/26138221/26138221_1.jpg', 130.00, 130.00, 'USD', 'available', 'regular', 'unisex', 93, true, true, NOW()),
('model_clarks_wallabee', 'brand_clarks', 'cat_everyday_casual', 'Wallabee', '26155516', 'Moccasin-style shoe with crepe sole', 'https://www.clarks.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-clarks-master-catalog/default/dw8c8b8b8b/images/26155516/26155516_1.jpg', 150.00, 150.00, 'USD', 'available', 'regular', 'unisex', 90, false, true, NOW()),
('model_clarks_un_costa_lace', 'brand_clarks', 'cat_everyday_casual', 'Un Costa Lace', '26159851', 'Casual lace-up with Unstructured technology', 'https://www.clarks.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-clarks-master-catalog/default/dw8c8b8b8b/images/26159851/26159851_1.jpg', 100.00, 100.00, 'USD', 'available', 'regular', 'men', 85, false, true, NOW()),
('model_clarks_cloudsteppers', 'brand_clarks', 'cat_specialty_comfort', 'Cloudsteppers', '26147742', 'Ultra-lightweight comfort shoe', 'https://www.clarks.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-clarks-master-catalog/default/dw8c8b8b8b/images/26147742/26147742_1.jpg', 80.00, 80.00, 'USD', 'available', 'regular', 'unisex', 87, false, true, NOW()),

-- TIER 3 BRANDS: Johnston & Murphy Models (3 models)
('model_jm_xc4_waterproof_golf', 'brand_johnston_murphy', 'cat_dress_formal', 'XC4 Waterproof Golf', '25-2405', 'Waterproof golf shoe with XC4 technology', 'https://www.johnstonmurphy.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-johnstonmurphy-master-catalog/default/dw8c8b8b8b/images/25-2405/25-2405_1.jpg', 225.00, 225.00, 'USD', 'available', 'regular', 'men', 86, false, true, NOW()),
('model_jm_mcguffey_wingtip', 'brand_johnston_murphy', 'cat_dress_formal', 'McGuffey Wingtip', '20-9585', 'Classic wingtip dress shoe', 'https://www.johnstonmurphy.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-johnstonmurphy-master-catalog/default/dw8c8b8b8b/images/20-9585/20-9585_1.jpg', 195.00, 195.00, 'USD', 'available', 'regular', 'men', 88, false, true, NOW()),
('model_jm_prentiss_plain_toe', 'brand_johnston_murphy', 'cat_dress_formal', 'Prentiss Plain Toe', '25-2473', 'Modern dress shoe with XC4 comfort', 'https://www.johnstonmurphy.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-johnstonmurphy-master-catalog/default/dw8c8b8b8b/images/25-2473/25-2473_1.jpg', 175.00, 175.00, 'USD', 'available', 'regular', 'men', 84, false, true, NOW()),

-- TIER 3 BRANDS: Luxury Brands (Gucci, Louis Vuitton, Balenciaga) - 3 models each
('model_gucci_ace_sneaker', 'brand_gucci', 'cat_dress_formal', 'Ace Sneaker', '386750', 'Luxury leather sneaker with signature stripe', 'https://media.gucci.com/style/DarkGray_Center_0_0_2400x2400/1479904799/386750_A38G0_9061_001_100_0000_Light-Ace-leather-sneaker.jpg', 590.00, 590.00, 'USD', 'available', 'regular', 'unisex', 91, true, true, NOW()),
('model_gucci_rhyton_sneaker', 'brand_gucci', 'cat_dress_formal', 'Rhyton Sneaker', '498916', 'Chunky luxury sneaker with vintage logo', 'https://media.gucci.com/style/DarkGray_Center_0_0_2400x2400/1479904799/498916_A38G0_9061_001_100_0000_Light-Rhyton-leather-sneaker.jpg', 790.00, 790.00, 'USD', 'available', 'regular', 'unisex', 88, false, true, NOW()),
('model_gucci_jordaan_loafer', 'brand_gucci', 'cat_dress_formal', 'Jordaan Loafer', '406994', 'Horsebit leather loafer', 'https://media.gucci.com/style/DarkGray_Center_0_0_2400x2400/1479904799/406994_BLM00_1000_001_100_0000_Light-Gucci-Jordaan-leather-loafer.jpg', 730.00, 730.00, 'USD', 'available', 'regular', 'unisex', 89, false, true, NOW()),

('model_lv_trainer_sneaker', 'brand_louis_vuitton', 'cat_dress_formal', 'LV Trainer', '1A8S1W', 'Luxury sneaker inspired by vintage basketball shoes', 'https://us.louisvuitton.com/images/is/image/lv/1/PP_VP_L/louis-vuitton-lv-trainer-sneaker-shoes--1A8S1W_PM2_Front%20view.jpg', 1090.00, 1090.00, 'USD', 'available', 'regular', 'men', 87, true, true, NOW()),
('model_lv_archlight_sneaker', 'brand_louis_vuitton', 'cat_dress_formal', 'Archlight Sneaker', '1A65QI', 'Futuristic luxury sneaker with curved sole', 'https://us.louisvuitton.com/images/is/image/lv/1/PP_VP_L/louis-vuitton-archlight-sneaker-shoes--1A65QI_PM2_Front%20view.jpg', 1200.00, 1200.00, 'USD', 'available', 'regular', 'women', 85, false, true, NOW()),
('model_lv_run_away_sneaker', 'brand_louis_vuitton', 'cat_dress_formal', 'Run Away Sneaker', '1A5ASY', 'Luxury running-inspired sneaker', 'https://us.louisvuitton.com/images/is/image/lv/1/PP_VP_L/louis-vuitton-run-away-sneaker-shoes--1A5ASY_PM2_Front%20view.jpg', 995.00, 995.00, 'USD', 'available', 'regular', 'men', 86, false, true, NOW()),

('model_balenciaga_triple_s', 'brand_balenciaga', 'cat_dress_formal', 'Triple S', '524036', 'Chunky luxury sneaker with layered sole', 'https://www.balenciaga.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-balenciaga-master-catalog/default/dw8c8b8b8b/images/524036/524036_1.jpg', 1050.00, 1050.00, 'USD', 'available', 'regular', 'unisex', 89, true, true, NOW()),
('model_balenciaga_speed_trainer', 'brand_balenciaga', 'cat_dress_formal', 'Speed Trainer', '483503', 'Sock-like luxury sneaker', 'https://www.balenciaga.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-balenciaga-master-catalog/default/dw8c8b8b8b/images/483503/483503_1.jpg', 795.00, 795.00, 'USD', 'available', 'regular', 'unisex', 87, false, true, NOW()),
('model_balenciaga_track', 'brand_balenciaga', 'cat_dress_formal', 'Track', '542023', 'Technical luxury sneaker with mesh and nylon', 'https://www.balenciaga.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-balenciaga-master-catalog/default/dw8c8b8b8b/images/542023/542023_1.jpg', 895.00, 895.00, 'USD', 'available', 'regular', 'unisex', 84, false, true, NOW()),

-- TIER 3 BRANDS: Timberland Models (4 models)
('model_timberland_6_inch_premium', 'brand_timberland', 'cat_outdoor_hiking', '6-Inch Premium Boot', '10061', 'Iconic waterproof work boot', 'https://www.timberland.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-timberland-master-catalog/default/dw8c8b8b8b/images/10061/10061_1.jpg', 190.00, 190.00, 'USD', 'available', 'regular', 'unisex', 94, true, true, NOW()),
('model_timberland_white_ledge', 'brand_timberland', 'cat_outdoor_hiking', 'White Ledge', '12135', 'Mid-height hiking boot with waterproof protection', 'https://www.timberland.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-timberland-master-catalog/default/dw8c8b8b8b/images/12135/12135_1.jpg', 90.00, 90.00, 'USD', 'available', 'regular', 'unisex', 87, false, true, NOW()),
('model_timberland_euro_hiker', 'brand_timberland', 'cat_outdoor_hiking', 'Euro Hiker', '6200R', 'Lightweight hiking boot with padded collar', 'https://www.timberland.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-timberland-master-catalog/default/dw8c8b8b8b/images/6200R/6200R_1.jpg', 100.00, 100.00, 'USD', 'available', 'regular', 'unisex', 85, false, true, NOW()),
('model_timberland_field_trekker', 'brand_timberland', 'cat_outdoor_hiking', 'Field Trekker', 'A1XZM', 'Low-profile hiking shoe with ReBOTL fabric', 'https://www.timberland.com/dw/image/v2/BCZC_PRD/on/demandware.static/-/Sites-timberland-master-catalog/default/dw8c8b8b8b/images/A1XZM/A1XZM_1.jpg', 80.00, 80.00, 'USD', 'available', 'regular', 'unisex', 83, false, true, NOW()),

-- TIER 3 BRANDS: Dr. Martens Models (4 models)
('model_dr_martens_1460', 'brand_dr_martens', 'cat_dress_formal', '1460 Original', '11822006', 'Iconic 8-eye boot with air-cushioned sole', 'https://i1.adis.ws/i/drmartens/11822006.80.jpg?$large$', 170.00, 170.00, 'USD', 'available', 'regular', 'unisex', 95, true, true, NOW()),
('model_dr_martens_1461', 'brand_dr_martens', 'cat_everyday_casual', '1461 Oxford', '11838002', 'Classic 3-eye oxford shoe', 'https://i1.adis.ws/i/drmartens/11838002.80.jpg?$large$', 140.00, 140.00, 'USD', 'available', 'regular', 'unisex', 91, false, true, NOW()),
('model_dr_martens_2976', 'brand_dr_martens', 'cat_dress_formal', '2976 Chelsea Boot', '11853001', 'Elastic-sided ankle boot', 'https://i1.adis.ws/i/drmartens/11853001.80.jpg?$large$', 170.00, 170.00, 'USD', 'available', 'regular', 'unisex', 89, false, true, NOW()),
('model_dr_martens_jadon', 'brand_dr_martens', 'cat_everyday_casual', 'Jadon Platform Boot', '15265001', 'Platform boot with chunky sole', 'https://i1.adis.ws/i/drmartens/15265001.80.jpg?$large$', 200.00, 200.00, 'USD', 'available', 'regular', 'unisex', 87, false, true, NOW());

-- =====================================================
-- PHASE 5: RE-ENABLE RLS AND VERIFICATION
-- =====================================================

-- Re-enable RLS for security
ALTER TABLE shoe_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE shoe_brands ENABLE ROW LEVEL SECURITY;
ALTER TABLE shoe_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE brand_category_mapping ENABLE ROW LEVEL SECURITY;
ALTER TABLE shoe_sizes ENABLE ROW LEVEL SECURITY;

-- =====================================================
-- PHASE 6: DATABASE VERIFICATION AND SUMMARY
-- =====================================================

-- Verify the population results
SELECT 'Database Population Summary' as status;

SELECT
    'Categories' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_records
FROM shoe_categories
UNION ALL
SELECT
    'Brands' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_records
FROM shoe_brands
UNION ALL
SELECT
    'Models' as table_name,
    COUNT(*) as total_records,
    COUNT(CASE WHEN is_active = true THEN 1 END) as active_records
FROM shoe_models
UNION ALL
SELECT
    'Brand-Category Mappings' as table_name,
    COUNT(*) as total_records,
    COUNT(*) as active_records
FROM brand_category_mapping;

-- Category distribution analysis
SELECT
    c.name as category_name,
    COUNT(m.id) as model_count,
    ROUND(AVG(m.price_range_min), 2) as avg_min_price,
    ROUND(AVG(m.price_range_max), 2) as avg_max_price
FROM shoe_categories c
LEFT JOIN shoe_models m ON c.id = m.category_id
GROUP BY c.id, c.name
ORDER BY model_count DESC;

-- Brand distribution analysis
SELECT
    b.name as brand_name,
    COUNT(m.id) as model_count,
    COUNT(DISTINCT m.category_id) as category_count,
    ROUND(AVG(m.price_range_min), 2) as avg_min_price
FROM shoe_brands b
LEFT JOIN shoe_models m ON b.id = m.brand_id
GROUP BY b.id, b.name
ORDER BY model_count DESC;

-- Success message
SELECT
    'SUCCESS: FootFit shoe database populated with ' ||
    (SELECT COUNT(*) FROM shoe_models) ||
    ' models across ' ||
    (SELECT COUNT(*) FROM shoe_categories) ||
    ' categories and ' ||
    (SELECT COUNT(*) FROM shoe_brands) ||
    ' brands' as result;
