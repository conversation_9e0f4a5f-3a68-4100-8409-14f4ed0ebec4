import { useAuth } from '@/contexts/AuthContext';
import { router, useSegments } from 'expo-router';
import { useEffect } from 'react';

/**
 * Hook that handles automatic authentication-based redirects
 * Call this in your root layout or main app component
 */
export function useAuthRedirect() {
  const { user, loading } = useAuth();
  const segments = useSegments();

  useEffect(() => {
    if (loading) return; // Don't redirect while loading

    const inAuthGroup = segments[0] === 'auth';

    if (!user && !inAuthGroup) {
      // Redirect to login if user is not authenticated and not in auth group
      router.replace('/auth/login');
    } else if (user && inAuthGroup) {
      // Redirect to main app if user is authenticated and in auth group
      router.replace('/(tabs)' as any);
    }
  }, [user, loading, segments]);

  return { user, loading };
}

/**
 * Hook for components that require authentication
 * Returns authentication state and redirects if needed
 */
export function useRequireAuth() {
  const { user, loading } = useAuth();

  useEffect(() => {
    if (!loading && !user) {
      router.replace('/auth/login');
    }
  }, [user, loading]);

  return { user, loading, isAuthenticated: !!user };
}
