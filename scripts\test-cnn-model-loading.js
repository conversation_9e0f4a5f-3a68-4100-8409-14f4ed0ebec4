/**
 * FootFit CNN Model Loading Test Script
 * Tests TensorFlow.js model loading and basic inference functionality
 */

const fs = require('fs');
const path = require('path');

// Mock React Native environment for Node.js testing
global.navigator = { userAgent: 'node' };
global.window = {};
global.document = {};

// Import TensorFlow.js for Node.js testing
const tf = require('@tensorflow/tfjs-node');

class CNNModelLoadingTester {
  constructor() {
    this.modelPath = './ai-models/tfjs_model/model.json';
    this.model = null;
    this.testResults = {
      modelLoading: false,
      modelStructure: false,
      inputValidation: false,
      basicInference: false,
      outputValidation: false,
      overallScore: 0
    };
    this.issues = [];
  }

  async runModelLoadingTests() {
    console.log('🧪 FootFit CNN Model Loading Tests');
    console.log('===================================\n');

    // Test 1: Model Loading
    await this.testModelLoading();
    
    // Test 2: Model Structure Validation
    await this.testModelStructure();
    
    // Test 3: Input Validation
    await this.testInputValidation();
    
    // Test 4: Basic Inference
    await this.testBasicInference();
    
    // Test 5: Output Validation
    await this.testOutputValidation();
    
    // Generate report
    this.generateTestReport();
    
    return this.testResults;
  }

  async testModelLoading() {
    console.log('🔍 Test 1: Model Loading');
    console.log('========================');
    
    try {
      // Check if model files exist
      if (!fs.existsSync(this.modelPath)) {
        throw new Error(`Model file not found: ${this.modelPath}`);
      }
      
      const weightsPath = './ai-models/tfjs_model/weights.bin';
      if (!fs.existsSync(weightsPath)) {
        throw new Error(`Weights file not found: ${weightsPath}`);
      }
      
      console.log('  ✅ Model files exist');
      
      // Load the model
      console.log('  📥 Loading TensorFlow.js model...');
      this.model = await tf.loadLayersModel(`file://${path.resolve(this.modelPath)}`);
      
      if (this.model) {
        console.log('  ✅ Model loaded successfully');
        console.log(`    📊 Total parameters: ${this.model.countParams().toLocaleString()}`);
        console.log(`    📐 Input shape: [${this.model.inputs[0].shape.join(', ')}]`);
        console.log(`    📐 Output shape: [${this.model.outputs[0].shape.join(', ')}]`);
        
        this.testResults.modelLoading = true;
      } else {
        throw new Error('Model loaded but is null');
      }

    } catch (error) {
      console.log(`  ❌ Model loading failed: ${error.message}`);
      this.issues.push(`Model loading failed: ${error.message}`);
      this.testResults.modelLoading = false;
    }

    console.log(`\n📊 Model Loading: ${this.testResults.modelLoading ? 'PASS' : 'FAIL'}\n`);
  }

  async testModelStructure() {
    console.log('🔍 Test 2: Model Structure Validation');
    console.log('=====================================');
    
    try {
      if (!this.model) {
        throw new Error('Model not loaded');
      }

      // Check input shape
      const inputShape = this.model.inputs[0].shape;
      const expectedInputShape = [null, 224, 224, 3]; // [batch, height, width, channels]
      
      console.log('  📐 Input shape validation:');
      console.log(`    Expected: [${expectedInputShape.join(', ')}]`);
      console.log(`    Actual: [${inputShape.join(', ')}]`);
      
      const inputShapeValid = inputShape.length === 4 && 
                             inputShape[1] === 224 && 
                             inputShape[2] === 224 && 
                             inputShape[3] === 3;
      
      if (inputShapeValid) {
        console.log('    ✅ Input shape is correct');
      } else {
        console.log('    ⚠️  Input shape differs from expected');
        this.issues.push('Input shape differs from expected 224x224x3');
      }

      // Check output shape
      const outputShape = this.model.outputs[0].shape;
      const expectedOutputShape = [null, 3]; // [batch, measurements]
      
      console.log('  📐 Output shape validation:');
      console.log(`    Expected: [${expectedOutputShape.join(', ')}]`);
      console.log(`    Actual: [${outputShape.join(', ')}]`);
      
      const outputShapeValid = outputShape.length === 2 && outputShape[1] === 3;
      
      if (outputShapeValid) {
        console.log('    ✅ Output shape is correct (3 measurements)');
      } else {
        console.log('    ❌ Output shape incorrect');
        this.issues.push('Output shape should be [null, 3] for [length, width, arch_height]');
      }

      // Check layer structure
      console.log('  🏗️  Model architecture:');
      this.model.layers.forEach((layer, index) => {
        console.log(`    ${index + 1}. ${layer.constructor.name}: ${layer.name}`);
      });

      this.testResults.modelStructure = inputShapeValid && outputShapeValid;

    } catch (error) {
      console.log(`  ❌ Model structure validation failed: ${error.message}`);
      this.issues.push(`Model structure validation failed: ${error.message}`);
      this.testResults.modelStructure = false;
    }

    console.log(`\n📊 Model Structure: ${this.testResults.modelStructure ? 'PASS' : 'FAIL'}\n`);
  }

  async testInputValidation() {
    console.log('🔍 Test 3: Input Validation');
    console.log('===========================');
    
    try {
      if (!this.model) {
        throw new Error('Model not loaded');
      }

      // Test different input formats
      console.log('  🧪 Testing input tensor creation:');
      
      // Test 1: Random input tensor
      const randomInput = tf.randomNormal([1, 224, 224, 3]);
      console.log(`    ✅ Random tensor: shape [${randomInput.shape.join(', ')}]`);
      
      // Test 2: Zeros input tensor
      const zerosInput = tf.zeros([1, 224, 224, 3]);
      console.log(`    ✅ Zeros tensor: shape [${zerosInput.shape.join(', ')}]`);
      
      // Test 3: Normalized input (0-1 range)
      const normalizedInput = tf.randomUniform([1, 224, 224, 3], 0, 1);
      console.log(`    ✅ Normalized tensor: shape [${normalizedInput.shape.join(', ')}]`);
      
      // Clean up tensors
      randomInput.dispose();
      zerosInput.dispose();
      normalizedInput.dispose();
      
      this.testResults.inputValidation = true;

    } catch (error) {
      console.log(`  ❌ Input validation failed: ${error.message}`);
      this.issues.push(`Input validation failed: ${error.message}`);
      this.testResults.inputValidation = false;
    }

    console.log(`\n📊 Input Validation: ${this.testResults.inputValidation ? 'PASS' : 'FAIL'}\n`);
  }

  async testBasicInference() {
    console.log('🔍 Test 4: Basic Inference');
    console.log('==========================');
    
    try {
      if (!this.model) {
        throw new Error('Model not loaded');
      }

      console.log('  🧠 Running inference tests:');
      
      // Test inference with random input
      const testInput = tf.randomUniform([1, 224, 224, 3], 0, 1);
      console.log(`    📥 Input tensor: shape [${testInput.shape.join(', ')}]`);
      
      const startTime = Date.now();
      const prediction = this.model.predict(testInput);
      const inferenceTime = Date.now() - startTime;
      
      console.log(`    ⏱️  Inference time: ${inferenceTime}ms`);
      console.log(`    📤 Output tensor: shape [${prediction.shape.join(', ')}]`);
      
      // Get prediction values
      const predictionData = await prediction.data();
      console.log(`    📊 Prediction values: [${Array.from(predictionData).map(v => v.toFixed(4)).join(', ')}]`);
      
      // Validate output format
      if (predictionData.length === 3) {
        console.log('    ✅ Output contains 3 measurements (length, width, arch_height)');
        
        // Check if values are reasonable (not NaN, not infinite)
        const validValues = Array.from(predictionData).every(v => 
          !isNaN(v) && isFinite(v)
        );
        
        if (validValues) {
          console.log('    ✅ All output values are valid numbers');
        } else {
          console.log('    ⚠️  Some output values are NaN or infinite');
          this.issues.push('Model outputs contain invalid values');
        }
      } else {
        console.log('    ❌ Output should contain exactly 3 measurements');
        this.issues.push('Model output should contain exactly 3 measurements');
      }
      
      // Clean up tensors
      testInput.dispose();
      prediction.dispose();
      
      this.testResults.basicInference = predictionData.length === 3 && 
                                       Array.from(predictionData).every(v => !isNaN(v) && isFinite(v));

    } catch (error) {
      console.log(`  ❌ Basic inference failed: ${error.message}`);
      this.issues.push(`Basic inference failed: ${error.message}`);
      this.testResults.basicInference = false;
    }

    console.log(`\n📊 Basic Inference: ${this.testResults.basicInference ? 'PASS' : 'FAIL'}\n`);
  }

  async testOutputValidation() {
    console.log('🔍 Test 5: Output Validation');
    console.log('============================');
    
    try {
      if (!this.model) {
        throw new Error('Model not loaded');
      }

      console.log('  🧪 Testing output consistency:');
      
      // Run multiple predictions with same input
      const testInput = tf.ones([1, 224, 224, 3]);
      const predictions = [];
      
      for (let i = 0; i < 3; i++) {
        const prediction = this.model.predict(testInput);
        const data = await prediction.data();
        predictions.push(Array.from(data));
        prediction.dispose();
      }
      
      console.log('    📊 Multiple predictions with same input:');
      predictions.forEach((pred, index) => {
        console.log(`      ${index + 1}. [${pred.map(v => v.toFixed(4)).join(', ')}]`);
      });
      
      // Check consistency (should be identical for same input)
      const consistent = predictions.every(pred => 
        pred.every((val, idx) => Math.abs(val - predictions[0][idx]) < 1e-6)
      );
      
      if (consistent) {
        console.log('    ✅ Model outputs are consistent');
      } else {
        console.log('    ⚠️  Model outputs vary for same input (expected for mock model)');
      }
      
      // Test output range validation
      console.log('  📏 Testing output ranges:');
      const flatPredictions = predictions.flat();
      const minVal = Math.min(...flatPredictions);
      const maxVal = Math.max(...flatPredictions);
      
      console.log(`    📊 Value range: ${minVal.toFixed(4)} to ${maxVal.toFixed(4)}`);
      
      // For foot measurements, reasonable ranges would be:
      // Length: 15-35 cm, Width: 6-15 cm, Arch height: 1.5-4.0 cm
      // But mock model may output any values
      
      testInput.dispose();
      
      this.testResults.outputValidation = true;

    } catch (error) {
      console.log(`  ❌ Output validation failed: ${error.message}`);
      this.issues.push(`Output validation failed: ${error.message}`);
      this.testResults.outputValidation = false;
    }

    console.log(`\n📊 Output Validation: ${this.testResults.outputValidation ? 'PASS' : 'FAIL'}\n`);
  }

  generateTestReport() {
    console.log('📋 CNN MODEL LOADING TEST REPORT');
    console.log('=================================\n');

    // Calculate overall score
    const tests = Object.keys(this.testResults).filter(key => key !== 'overallScore');
    const passedTests = tests.filter(test => this.testResults[test]).length;
    this.testResults.overallScore = Math.round((passedTests / tests.length) * 100);

    console.log('📊 TEST RESULTS:');
    console.log(`  Model Loading: ${this.testResults.modelLoading ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  Model Structure: ${this.testResults.modelStructure ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  Input Validation: ${this.testResults.inputValidation ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  Basic Inference: ${this.testResults.basicInference ? '✅ PASS' : '❌ FAIL'}`);
    console.log(`  Output Validation: ${this.testResults.outputValidation ? '✅ PASS' : '❌ FAIL'}`);

    console.log(`\n🏆 OVERALL SCORE: ${this.testResults.overallScore}%`);

    if (this.testResults.overallScore >= 80) {
      console.log('🎉 EXCELLENT - CNN model loading pipeline is ready!');
    } else if (this.testResults.overallScore >= 60) {
      console.log('✅ GOOD - Most functionality working with minor issues');
    } else {
      console.log('⚠️  NEEDS ATTENTION - Significant issues with model loading');
    }

    if (this.issues.length > 0) {
      console.log('\n⚠️  ISSUES IDENTIFIED:');
      this.issues.forEach((issue, index) => {
        console.log(`  ${index + 1}. ${issue}`);
      });
    } else {
      console.log('\n🎉 NO ISSUES FOUND - Perfect model loading!');
    }

    console.log('\n🎓 ACADEMIC PROJECT STATUS:');
    if (this.testResults.modelLoading) {
      console.log('  ✅ TensorFlow.js model loading works');
    } else {
      console.log('  ❌ Model loading needs fixing');
    }
    
    if (this.testResults.basicInference) {
      console.log('  ✅ CNN inference pipeline functional');
    } else {
      console.log('  ❌ CNN inference needs debugging');
    }

    console.log('\n🚀 NEXT STEPS:');
    if (this.testResults.overallScore >= 80) {
      console.log('  1. Integrate with React Native app');
      console.log('  2. Test image preprocessing pipeline');
      console.log('  3. Validate foot analysis workflow');
      console.log('  4. Replace mock model with real trained model');
    } else {
      console.log('  1. Fix identified issues');
      console.log('  2. Re-run model loading tests');
      console.log('  3. Debug TensorFlow.js integration');
    }
  }
}

async function main() {
  try {
    console.log('🚀 Starting CNN Model Loading Tests...\n');
    
    const tester = new CNNModelLoadingTester();
    const results = await tester.runModelLoadingTests();
    
    console.log('\n🎉 CNN MODEL LOADING TESTS COMPLETE');
    console.log('===================================');
    console.log(`Model loading readiness: ${results.overallScore}%`);
    
  } catch (error) {
    console.error('❌ Model loading tests failed:', error.message);
    process.exit(1);
  }
}

main();
