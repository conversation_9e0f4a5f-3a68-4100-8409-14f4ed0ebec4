import * as Haptics from 'expo-haptics';
import { router } from 'expo-router';
import { useState } from 'react';
import { ScrollView, StyleSheet, TouchableOpacity, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';

export default function SetPreferencesScreen() {
  const { colors } = useTheme();
  const { updateProfile } = useAuth();
  
  const [preferences, setPreferences] = useState({
    preferred_brands: [] as string[],
    preferred_unit: 'cm' as 'cm' | 'inches',
    known_shoe_size: '',
  });
  const [saving, setSaving] = useState(false);

  const availableBrands = ['Nike', 'Adidas', 'Converse', 'Vans', 'New Balance', 'Puma', 'Reebok'];
  const shoeSizes = ['6', '6.5', '7', '7.5', '8', '8.5', '9', '9.5', '10', '10.5', '11', '11.5', '12'];

  const toggleBrand = (brand: string) => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setPreferences(prev => ({
      ...prev,
      preferred_brands: prev.preferred_brands.includes(brand)
        ? prev.preferred_brands.filter(b => b !== brand)
        : [...prev.preferred_brands, brand]
    }));
  };

  const handleSavePreferences = async () => {
    try {
      setSaving(true);
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);

      const { error } = await updateProfile(preferences);
      
      if (error) {
        throw error;
      }

      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      router.replace('/(tabs)');
    } catch (error) {
      console.error('Failed to save preferences:', error);
      Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);
      // Continue anyway for better UX
      router.replace('/(tabs)');
    } finally {
      setSaving(false);
    }
  };

  const handleSkip = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    router.replace('/(tabs)');
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <ThemedView style={styles.header}>
        <View style={styles.headerContent}>
          <View style={[styles.logo, { backgroundColor: colors.primary }]}>
            <IconSymbol
              size={32}
              name="figure.walk"
              color={colors.textInverse}
            />
          </View>
          <ThemedText variant="h2" color="primary">Welcome to FootFit!</ThemedText>
          <ThemedText variant="body" color="secondary" style={styles.subtitle}>
            Let&apos;s personalize your experience
          </ThemedText>
        </View>
      </ThemedView>

      <ScrollView style={styles.scrollView} showsVerticalScrollIndicator={false}>
        {/* Favorite Brands */}
        <ThemedView style={styles.section}>
          <View style={styles.sectionHeader}>
            <IconSymbol size={24} name="heart.fill" color={colors.primary} />
            <ThemedText variant="h3" style={styles.sectionTitle}>
              Favorite Shoe Brands
            </ThemedText>
          </View>
          <ThemedText variant="body" color="secondary" style={styles.sectionDescription}>
            Select brands you love to get personalized recommendations
          </ThemedText>
          
          <View style={styles.brandsGrid}>
            {availableBrands.map((brand) => (
              <TouchableOpacity
                key={brand}
                style={[
                  styles.brandChip,
                  {
                    backgroundColor: preferences.preferred_brands.includes(brand) ? colors.primary : colors.backgroundSecondary,
                    borderColor: preferences.preferred_brands.includes(brand) ? colors.primary : colors.border,
                  }
                ]}
                onPress={() => toggleBrand(brand)}
                activeOpacity={0.7}
              >
                <ThemedText
                  variant="labelLarge"
                  color={preferences.preferred_brands.includes(brand) ? 'inverse' : 'primary'}
                >
                  {brand}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </ThemedView>

        {/* Measurement Unit */}
        <ThemedView style={styles.section}>
          <View style={styles.sectionHeader}>
            <IconSymbol size={24} name="ruler" color={colors.primary} />
            <ThemedText variant="h3" style={styles.sectionTitle}>
              Measurement Unit
            </ThemedText>
          </View>
          <ThemedText variant="body" color="secondary" style={styles.sectionDescription}>
            Choose your preferred unit for foot measurements
          </ThemedText>
          
          <View style={styles.unitSelector}>
            {[
              { value: 'cm', label: 'Centimeters (cm)' },
              { value: 'inches', label: 'Inches (in)' }
            ].map((unit) => (
              <TouchableOpacity
                key={unit.value}
                style={[
                  styles.unitOption,
                  {
                    backgroundColor: preferences.preferred_unit === unit.value ? colors.primary : colors.backgroundSecondary,
                    borderColor: preferences.preferred_unit === unit.value ? colors.primary : colors.border,
                  }
                ]}
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  setPreferences(prev => ({ ...prev, preferred_unit: unit.value as 'cm' | 'inches' }));
                }}
                activeOpacity={0.7}
              >
                <ThemedText
                  variant="labelLarge"
                  color={preferences.preferred_unit === unit.value ? 'inverse' : 'primary'}
                >
                  {unit.label}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </ThemedView>

        {/* Known Shoe Size */}
        <ThemedView style={styles.section}>
          <View style={styles.sectionHeader}>
            <IconSymbol size={24} name="shoeprints.fill" color={colors.primary} />
            <ThemedText variant="h3" style={styles.sectionTitle}>
              Known Shoe Size (Optional)
            </ThemedText>
          </View>
          <ThemedText variant="body" color="secondary" style={styles.sectionDescription}>
            If you know your shoe size, select it to help improve recommendations
          </ThemedText>
          
          <View style={styles.sizesGrid}>
            <TouchableOpacity
              style={[
                styles.sizeChip,
                {
                  backgroundColor: preferences.known_shoe_size === '' ? colors.primary : colors.backgroundSecondary,
                  borderColor: preferences.known_shoe_size === '' ? colors.primary : colors.border,
                }
              ]}
              onPress={() => {
                Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                setPreferences(prev => ({ ...prev, known_shoe_size: '' }));
              }}
              activeOpacity={0.7}
            >
              <ThemedText
                variant="labelLarge"
                color={preferences.known_shoe_size === '' ? 'inverse' : 'primary'}
              >
                I don&apos;t know yet
              </ThemedText>
            </TouchableOpacity>
            
            {shoeSizes.map((size) => (
              <TouchableOpacity
                key={size}
                style={[
                  styles.sizeChip,
                  {
                    backgroundColor: preferences.known_shoe_size === size ? colors.primary : colors.backgroundSecondary,
                    borderColor: preferences.known_shoe_size === size ? colors.primary : colors.border,
                  }
                ]}
                onPress={() => {
                  Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
                  setPreferences(prev => ({ ...prev, known_shoe_size: size }));
                }}
                activeOpacity={0.7}
              >
                <ThemedText
                  variant="labelLarge"
                  color={preferences.known_shoe_size === size ? 'inverse' : 'primary'}
                >
                  UK {size}
                </ThemedText>
              </TouchableOpacity>
            ))}
          </View>
        </ThemedView>

        {/* Bottom spacing */}
        <View style={styles.bottomSpacing} />
      </ScrollView>

      {/* Bottom Actions */}
      <ThemedView style={styles.bottomActions}>
        <TouchableOpacity
          style={[styles.skipButton, { borderColor: colors.border }]}
          onPress={handleSkip}
          activeOpacity={0.7}
        >
          <ThemedText variant="labelLarge" color="secondary">
            Skip for now
          </ThemedText>
        </TouchableOpacity>
        
        <TouchableOpacity
          style={[
            styles.saveButton,
            { 
              backgroundColor: saving ? colors.backgroundSecondary : colors.primary,
              opacity: saving ? 0.6 : 1
            }
          ]}
          onPress={handleSavePreferences}
          disabled={saving}
          activeOpacity={0.8}
        >
          <ThemedText variant="labelLarge" color={saving ? 'secondary' : 'inverse'}>
            {saving ? 'Saving...' : 'Get Started'}
          </ThemedText>
        </TouchableOpacity>
      </ThemedView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  headerContent: {
    alignItems: 'center',
    gap: 12,
  },
  logo: {
    width: 64,
    height: 64,
    borderRadius: 32,
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 8,
  },
  subtitle: {
    textAlign: 'center',
    marginTop: 4,
  },
  scrollView: {
    flex: 1,
  },
  section: {
    paddingHorizontal: 20,
    paddingBottom: 32,
  },
  sectionHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
    marginBottom: 8,
  },
  sectionTitle: {
    flex: 1,
  },
  sectionDescription: {
    marginBottom: 20,
    lineHeight: 20,
  },
  brandsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  brandChip: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
  },
  unitSelector: {
    gap: 12,
  },
  unitOption: {
    paddingVertical: 16,
    paddingHorizontal: 20,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
  },
  sizesGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: 12,
  },
  sizeChip: {
    paddingHorizontal: 16,
    paddingVertical: 10,
    borderRadius: 20,
    borderWidth: 1,
    minWidth: 80,
    alignItems: 'center',
  },
  bottomSpacing: {
    height: 20,
  },
  bottomActions: {
    flexDirection: 'row',
    paddingHorizontal: 20,
    paddingVertical: 20,
    gap: 12,
  },
  skipButton: {
    flex: 1,
    paddingVertical: 16,
    borderRadius: 12,
    borderWidth: 1,
    alignItems: 'center',
  },
  saveButton: {
    flex: 2,
    paddingVertical: 16,
    borderRadius: 12,
    alignItems: 'center',
  },
});
