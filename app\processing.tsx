import { router, useLocalSearchParams } from 'expo-router';
import { useCallback, useEffect, useMemo, useState } from 'react';
import { Image, StyleSheet, View } from 'react-native';
import { SafeAreaView } from 'react-native-safe-area-context';

import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { useAuth } from '@/contexts/AuthContext';
import { useTheme } from '@/contexts/ThemeContext';
import * as Haptics from 'expo-haptics';
import * as ImageManipulator from 'expo-image-manipulator';

type ProcessingState = 'uploading' | 'analyzing' | 'complete' | 'error';

export default function ProcessingScreen() {
  const { colors } = useTheme();
  const { user, profile, loading: authLoading } = useAuth();
  const { imageUri } = useLocalSearchParams<{ imageUri: string }>();
  const [processingState, setProcessingState] = useState<ProcessingState>('uploading');
  const [progress, setProgress] = useState(0);

  const [error, setError] = useState<string | null>(null);

  // Memoize user preferences to prevent object recreation on every render
  const userPreferences = useMemo(() => ({
    preferred_brands: profile?.preferred_brands || [],
    preferred_categories: [], // Will be selected in results page
    preferred_fit: 'regular' as const,
  }), [profile?.preferred_brands]);

  // Define processImage function first - memoized to prevent re-creation
  const processImage = useCallback(async () => {
    try {
      if (!user) {
        throw new Error('User not authenticated');
      }

      // Step 0: Process image (compress, resize to square, optimize)
      setProcessingState('uploading');
      setProgress(2);

      // Simple image processing using expo-image-manipulator
      const processResult = await ImageManipulator.manipulateAsync(
        imageUri,
        [
          { resize: { width: 1024, height: 1024 } }
        ],
        {
          compress: 0.8,
          format: ImageManipulator.SaveFormat.JPEG,
        }
      );

      setProgress(5);

      // Basic image validation
      const validationResult = {
        warnings: processResult.width !== processResult.height ? ['Image is not square'] : []
      };

      // Log warnings if any
      if (validationResult.warnings.length > 0) {
        const { log } = await import('@/utils/logger');
        log.warn('Image validation warnings', 'ProcessingScreen', validationResult.warnings);
      }

      // Step 2: Upload processed image to Supabase Storage
      setProgress(10);

      // Simulate upload progress
      const uploadInterval = setInterval(() => {
        setProgress(prev => {
          if (prev >= 30) {
            clearInterval(uploadInterval);
            return 30;
          }
          return prev + 5;
        });
      }, 100);

      // Import services dynamically to avoid import issues
      const { SupabaseService } = await import('@/services/supabaseService');
      const uploadResult = await SupabaseService.uploadFootImage(user.id, processResult.uri);
      clearInterval(uploadInterval);

      if (!uploadResult.success) {
        throw new Error(uploadResult.error || 'Upload failed');
      }

      // Step 2: Real AI Analysis using TensorFlow.js CNN
      setProcessingState('analyzing');
      setProgress(40);

      // Import consolidated AI service for genuine AI processing
      const { FootAnalysisAI } = await import('@/services/footAnalysisAI');

      // Initialize AI service if not already done
      const { log } = await import('@/utils/logger');
      log.info('Initializing FootFit AI Analysis Service for measurement', 'ProcessingScreen');
      const initialized = await FootAnalysisAI.initialize();

      if (!initialized) {
        throw new Error('Failed to initialize AI service');
      }

      // Use memoized user preferences for personalized recommendations
      const measurementResult = await FootAnalysisAI.measureFoot({
        image_url: uploadResult.url || processResult.uri,
        user_preferences: userPreferences,
      });

      setProgress(90);

      if (!measurementResult.success) {
        // Check if this is a foot detection error
        if (measurementResult.error?.includes('foot_detection_failed')) {
          throw new Error('No feet detected in the image. Please ensure your feet are clearly visible and try again.');
        }
        throw new Error(measurementResult.error || 'Analysis failed');
      }

      // Get measurement data for navigation
      const measurementData = measurementResult.data!;

      // Step 3: Complete processing (no automatic saving)
      setProgress(100);
      setProcessingState('complete');

      // Success haptic feedback
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);

      // Navigate to results after a brief delay
      setTimeout(() => {
        router.replace({
          pathname: '/results',
          params: {
            imageUri,
            measurementData: JSON.stringify(measurementData),
            // No measurementId since we're not auto-saving
          }
        });
      }, 1000);

    } catch (error) {
      const { log } = await import('@/utils/logger');
      log.error('Processing error', 'ProcessingScreen', error);

      // Error haptic feedback
      await Haptics.notificationAsync(Haptics.NotificationFeedbackType.Error);

      const errorMessage = error instanceof Error ? error.message : 'Processing failed';

      // Provide specific guidance for foot detection errors
      if (errorMessage.includes('feet detected') || errorMessage.includes('foot_detection_failed')) {
        setError('No feet detected in the image. Please:\n• Ensure both feet are clearly visible\n• Use good lighting\n• Remove socks or shoes\n• Position feet flat on the ground');
      } else {
        setError(errorMessage);
      }

      setProcessingState('error');
    }
  }, [imageUri, user, userPreferences]);

  // useEffect to handle authentication and start processing
  useEffect(() => {
    if (!authLoading && !user) {
      // Redirect to login if not authenticated
      router.replace('/auth/login');
      return;
    }

    if (imageUri && user) {
      processImage();
    } else if (!imageUri) {
      setError('No image provided');
      setProcessingState('error');
    }
  }, [imageUri, authLoading, user, processImage]);

  const handleRetry = () => {
    setError(null);
    setProgress(0);
    processImage();
  };

  const handleGoBack = () => {
    router.back();
  };

  const getStatusText = () => {
    switch (processingState) {
      case 'uploading':
        return 'Uploading image...';
      case 'analyzing':
        return 'Analyzing your foot...';
      case 'complete':
        return 'Analysis complete!';
      case 'error':
        return 'Processing failed';
      default:
        return 'Processing...';
    }
  };

  const getStatusIcon = () => {
    switch (processingState) {
      case 'uploading':
        return 'icloud.and.arrow.up';
      case 'analyzing':
        return 'brain.head.profile';
      case 'complete':
        return 'checkmark.circle.fill';
      case 'error':
        return 'exclamationmark.triangle.fill';
      default:
        return 'gear';
    }
  };

  const getStatusColor = () => {
    switch (processingState) {
      case 'complete':
        return colors.success;
      case 'error':
        return colors.error;
      default:
        return colors.primary;
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: colors.background }]}>
      {/* Header */}
      <ThemedView style={styles.header}>
        <Button
          title=""
          onPress={handleGoBack}
          variant="ghost"
          size="small"
          icon="chevron.left"
          style={styles.backButton}
        />
        <ThemedText variant="h3">Processing</ThemedText>
        <View style={styles.headerSpacer} />
      </ThemedView>

      <View style={styles.content}>
        {/* Image Preview */}
        {imageUri && (
          <View style={styles.imageContainer}>
            <Image 
              source={{ uri: imageUri }} 
              style={[styles.image, { borderColor: colors.border }]}
              resizeMode="cover"
            />
          </View>
        )}

        {/* Status */}
        <View style={styles.statusContainer}>
          <View style={[styles.statusIcon, { backgroundColor: getStatusColor() + '20' }]}>
            <IconSymbol
              size={48}
              name={getStatusIcon()}
              color={getStatusColor()}
            />
          </View>
          
          <ThemedText variant="h3" style={styles.statusText}>
            {getStatusText()}
          </ThemedText>

          {processingState !== 'error' && processingState !== 'complete' && (
            <View style={styles.progressContainer}>
              <View style={[styles.progressBar, { backgroundColor: colors.backgroundSecondary }]}>
                <View 
                  style={[
                    styles.progressFill, 
                    { 
                      backgroundColor: colors.primary,
                      width: `${progress}%`
                    }
                  ]} 
                />
              </View>
              <ThemedText variant="caption" color="secondary">
                {Math.round(progress)}% complete
              </ThemedText>
            </View>
          )}

          {processingState === 'error' && error && (
            <View style={styles.errorContainer}>
              <ThemedText variant="body" color="error" style={styles.errorText}>
                {error}
              </ThemedText>
              <Button
                title="Try Again"
                onPress={handleRetry}
                variant="primary"
                size="medium"
                icon="arrow.clockwise"
              />
            </View>
          )}

          {processingState === 'complete' && (
            <View style={styles.completeContainer}>
              <ThemedText variant="body" color="secondary" style={styles.completeText}>
                Redirecting to results...
              </ThemedText>
            </View>
          )}
        </View>

        {/* Processing Steps */}
        {processingState !== 'error' && (
          <View style={styles.stepsContainer}>
            <ThemedText variant="h4" style={styles.stepsTitle}>
              Processing Steps
            </ThemedText>
            
            <View style={styles.stepsList}>
              <View style={styles.step}>
                <View style={[
                  styles.stepIndicator,
                  { 
                    backgroundColor: progress >= 30 ? colors.success : colors.backgroundSecondary,
                  }
                ]}>
                  <IconSymbol
                    size={16}
                    name={progress >= 30 ? "checkmark" : "1.circle"}
                    color={progress >= 30 ? colors.textInverse : colors.textSecondary}
                  />
                </View>
                <ThemedText variant="body" color={progress >= 30 ? "primary" : "secondary"}>
                  Upload image to secure storage
                </ThemedText>
              </View>

              <View style={styles.step}>
                <View style={[
                  styles.stepIndicator,
                  { 
                    backgroundColor: progress >= 90 ? colors.success : colors.backgroundSecondary,
                  }
                ]}>
                  <IconSymbol
                    size={16}
                    name={progress >= 90 ? "checkmark" : "2.circle"}
                    color={progress >= 90 ? colors.textInverse : colors.textSecondary}
                  />
                </View>
                <ThemedText variant="body" color={progress >= 90 ? "primary" : "secondary"}>
                  AI analysis and measurement
                </ThemedText>
              </View>

              <View style={styles.step}>
                <View style={[
                  styles.stepIndicator,
                  { 
                    backgroundColor: progress >= 100 ? colors.success : colors.backgroundSecondary,
                  }
                ]}>
                  <IconSymbol
                    size={16}
                    name={progress >= 100 ? "checkmark" : "3.circle"}
                    color={progress >= 100 ? colors.textInverse : colors.textSecondary}
                  />
                </View>
                <ThemedText variant="body" color={progress >= 100 ? "primary" : "secondary"}>
                  Generate recommendations
                </ThemedText>
              </View>
            </View>
          </View>
        )}
      </View>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  backButton: {
    width: 40,
  },
  headerSpacer: {
    width: 40,
  },
  content: {
    flex: 1,
    paddingHorizontal: 20,
    paddingTop: 20,
  },
  imageContainer: {
    alignItems: 'center',
    marginBottom: 32,
  },
  image: {
    width: 200,
    height: 200,
    borderRadius: 12,
    borderWidth: 2,
  },
  statusContainer: {
    alignItems: 'center',
    marginBottom: 40,
    gap: 16,
  },
  statusIcon: {
    width: 80,
    height: 80,
    borderRadius: 40,
    alignItems: 'center',
    justifyContent: 'center',
  },
  statusText: {
    textAlign: 'center',
  },
  progressContainer: {
    width: '100%',
    alignItems: 'center',
    gap: 8,
  },
  progressBar: {
    width: '100%',
    height: 8,
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    borderRadius: 4,
  },
  errorContainer: {
    alignItems: 'center',
    gap: 16,
  },
  errorText: {
    textAlign: 'center',
    maxWidth: 280,
  },
  completeContainer: {
    alignItems: 'center',
  },
  completeText: {
    textAlign: 'center',
  },
  stepsContainer: {
    gap: 16,
  },
  stepsTitle: {
    textAlign: 'center',
  },
  stepsList: {
    gap: 16,
  },
  step: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  stepIndicator: {
    width: 32,
    height: 32,
    borderRadius: 16,
    alignItems: 'center',
    justifyContent: 'center',
  },
});
