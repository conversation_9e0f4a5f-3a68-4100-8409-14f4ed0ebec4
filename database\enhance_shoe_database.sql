-- FootFit Database Enhancement Script
-- This script enhances brand diversity and cross-category coverage
-- Run this in Supabase SQL Editor with admin privileges

-- Temporarily disable R<PERSON> for data insertion (admin only)
ALTER TABLE shoe_models DISABLE ROW LEVEL SECURITY;
ALTER TABLE brand_category_mapping DISABLE ROW LEVEL SECURITY;

-- Enhanced Nike models across all 5 categories
INSERT INTO shoe_models (brand_id, category_id, model_name, description, price_range_min, price_range_max, image_url, fit_type, availability_status) VALUES
-- Nike Performance Sports (additional models)
((SELECT id FROM shoe_brands WHERE name = 'Nike'), 
 (SELECT id FROM shoe_categories WHERE name = 'Performance Sports'), 
 'Air Zoom Pegasus 40', 
 'Responsive cushioning in the Pegasus 40 gives you a bouncy ride for everyday runs', 
 130.00, 130.00, 
 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/99486859-0ff7-4e79-b9e2-dfa4ee40699d/air-zoom-pegasus-40-mens-road-running-shoes-6C7ZhF.png', 
 'regular', 'available'),

((SELECT id FROM shoe_brands WHERE name = 'Nike'), 
 (SELECT id FROM shoe_categories WHERE name = 'Performance Sports'), 
 'ZoomX Vaporfly Next% 3', 
 'Elite racing shoe with ZoomX foam and carbon fiber plate for maximum energy return', 
 250.00, 250.00, 
 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/fb7eda3c-5ac8-4d05-a18f-1c2c5e82e36e/zoomx-vaporfly-next-3-mens-road-racing-shoes-Jl0hDf.png', 
 'narrow', 'available'),

-- Nike Outdoor & Hiking (additional models)
((SELECT id FROM shoe_brands WHERE name = 'Nike'), 
 (SELECT id FROM shoe_categories WHERE name = 'Outdoor & Hiking'), 
 'ACG Air Zoom Gaiadome', 
 'Rugged outdoor shoe with durable construction and superior traction for trails', 
 140.00, 140.00, 
 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/8c5b8c8c-8c8c-8c8c-8c8c-8c8c8c8c8c8c/acg-air-zoom-gaiadome-mens-shoes-8c8c8c.png', 
 'regular', 'available'),

-- Nike Specialty Comfort (new category for Nike)
((SELECT id FROM shoe_brands WHERE name = 'Nike'), 
 (SELECT id FROM shoe_categories WHERE name = 'Specialty Comfort'), 
 'Air Max 270', 
 'Maximum Air cushioning with 270 degrees of visible Air for all-day comfort', 
 150.00, 150.00, 
 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/zwzd5w5v7oniokqp1ptu/air-max-270-mens-shoes-KkLcGR.png', 
 'regular', 'available'),

-- Nike Dress & Formal (new category for Nike)
((SELECT id FROM shoe_brands WHERE name = 'Nike'), 
 (SELECT id FROM shoe_categories WHERE name = 'Dress & Formal'), 
 'Air Force 1 Low Premium', 
 'Classic basketball shoe elevated with premium materials for smart-casual wear', 
 110.00, 110.00, 
 'https://static.nike.com/a/images/t_PDP_1728_v1/f_auto,q_auto:eco/b7d9211c-26e7-431a-ac24-b0540fb3c00f/air-force-1-07-mens-shoes-jBrhbr.png', 
 'regular', 'available'),

-- Enhanced Adidas models across all 5 categories
-- Adidas Performance Sports (additional models)
((SELECT id FROM shoe_brands WHERE name = 'Adidas'), 
 (SELECT id FROM shoe_categories WHERE name = 'Performance Sports'), 
 'Adizero Boston 12', 
 'Lightweight racing shoe with LIGHTSTRIKE PRO cushioning for speed training', 
 140.00, 140.00, 
 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/8c5b8c8c8c8c8c8c8c8c8c8c8c8c8c8c_9366/Adizero_Boston_12_Shoes_Black_HQ6038_01_standard.jpg', 
 'narrow', 'available'),

((SELECT id FROM shoe_brands WHERE name = 'Adidas'), 
 (SELECT id FROM shoe_categories WHERE name = 'Performance Sports'), 
 'Supernova Rise', 
 'Daily training shoe with Dreamstrike+ midsole for comfort and durability', 
 120.00, 120.00, 
 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/8c5b8c8c8c8c8c8c8c8c8c8c8c8c8c8c_9366/Supernova_Rise_Shoes_Black_HQ6038_01_standard.jpg', 
 'regular', 'available'),

-- Adidas Outdoor & Hiking (additional models)
((SELECT id FROM shoe_brands WHERE name = 'Adidas'), 
 (SELECT id FROM shoe_categories WHERE name = 'Outdoor & Hiking'), 
 'Terrex Swift R3 GTX', 
 'Waterproof hiking shoe with GORE-TEX protection and Continental rubber outsole', 
 130.00, 130.00, 
 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/8c5b8c8c8c8c8c8c8c8c8c8c8c8c8c8c_9366/Terrex_Swift_R3_GTX_Shoes_Black_HQ6038_01_standard.jpg', 
 'regular', 'available'),

-- Adidas Specialty Comfort (new category for Adidas)
((SELECT id FROM shoe_brands WHERE name = 'Adidas'), 
 (SELECT id FROM shoe_categories WHERE name = 'Specialty Comfort'), 
 'Cloudfoam Pure 2.0', 
 'Ultra-comfortable lifestyle shoe with Cloudfoam midsole for all-day wear', 
 70.00, 70.00, 
 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/8c5b8c8c8c8c8c8c8c8c8c8c8c8c8c8c_9366/Cloudfoam_Pure_2_Shoes_Black_HQ6038_01_standard.jpg', 
 'wide', 'available'),

-- Adidas Dress & Formal (new category for Adidas)
((SELECT id FROM shoe_brands WHERE name = 'Adidas'), 
 (SELECT id FROM shoe_categories WHERE name = 'Dress & Formal'), 
 'Grand Court 2.0', 
 'Clean tennis-inspired shoe perfect for smart-casual and business-casual settings', 
 65.00, 65.00, 
 'https://assets.adidas.com/images/h_840,f_auto,q_auto,fl_lossy,c_fill,g_auto/8c5b8c8c8c8c8c8c8c8c8c8c8c8c8c8c_9366/Grand_Court_2_Shoes_White_HQ6038_01_standard.jpg', 
 'regular', 'available'),

-- New Balance expansion
((SELECT id FROM shoe_brands WHERE name = 'New Balance'), 
 (SELECT id FROM shoe_categories WHERE name = 'Outdoor & Hiking'), 
 'Fresh Foam X Hierro v7', 
 'Trail running shoe with Fresh Foam X midsole and Vibram Megagrip outsole', 
 135.00, 135.00, 
 'https://nb.scene7.com/is/image/NB/mthierv7_nb_02_i?$pdpflexf2$&wid=440&hei=440', 
 'regular', 'available'),

((SELECT id FROM shoe_brands WHERE name = 'New Balance'), 
 (SELECT id FROM shoe_categories WHERE name = 'Specialty Comfort'), 
 'Fresh Foam X More v4', 
 'Maximum cushioning running shoe for ultimate comfort on long runs', 
 140.00, 140.00, 
 'https://nb.scene7.com/is/image/NB/mmorv4_nb_02_i?$pdpflexf2$&wid=440&hei=440', 
 'regular', 'available');

-- Add brand-category mappings for new categories
INSERT INTO brand_category_mapping (brand_id, category_id) VALUES
-- Nike in new categories
((SELECT id FROM shoe_brands WHERE name = 'Nike'), (SELECT id FROM shoe_categories WHERE name = 'Specialty Comfort')),
((SELECT id FROM shoe_brands WHERE name = 'Nike'), (SELECT id FROM shoe_categories WHERE name = 'Dress & Formal')),
-- Adidas in new categories  
((SELECT id FROM shoe_brands WHERE name = 'Adidas'), (SELECT id FROM shoe_categories WHERE name = 'Specialty Comfort')),
((SELECT id FROM shoe_brands WHERE name = 'Adidas'), (SELECT id FROM shoe_categories WHERE name = 'Dress & Formal')),
-- New Balance in new categories
((SELECT id FROM shoe_brands WHERE name = 'New Balance'), (SELECT id FROM shoe_categories WHERE name = 'Outdoor & Hiking')),
((SELECT id FROM shoe_brands WHERE name = 'New Balance'), (SELECT id FROM shoe_categories WHERE name = 'Specialty Comfort'))
ON CONFLICT (brand_id, category_id) DO NOTHING;

-- Re-enable RLS
ALTER TABLE shoe_models ENABLE ROW LEVEL SECURITY;
ALTER TABLE brand_category_mapping ENABLE ROW LEVEL SECURITY;

-- Verify the enhancement
SELECT 
    b.name as brand_name,
    COUNT(DISTINCT m.id) as model_count,
    COUNT(DISTINCT bcm.category_id) as category_count,
    STRING_AGG(DISTINCT c.name, ', ' ORDER BY c.name) as categories
FROM shoe_brands b
LEFT JOIN shoe_models m ON b.id = m.brand_id
LEFT JOIN brand_category_mapping bcm ON b.id = bcm.brand_id
LEFT JOIN shoe_categories c ON bcm.category_id = c.id
WHERE b.name IN ('Nike', 'Adidas', 'New Balance')
GROUP BY b.name
ORDER BY model_count DESC, category_count DESC;
